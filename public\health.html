<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Grok 2 API - 管理面板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            color: #4a5568;
            margin-bottom: 20px;
            font-size: 1.5rem;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .screenshot-container {
            text-align: center;
        }

        .screenshot {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            margin-bottom: 15px;
        }

        .config-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e2e8f0;
        }

        .config-item:last-child {
            border-bottom: none;
        }

        .config-label {
            font-weight: 600;
            color: #4a5568;
        }

        .config-value {
            color: #2d3748;
            font-family: 'Courier New', monospace;
            background: #f7fafc;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background-color: #48bb78;
            animation: pulse 2s infinite;
        }

        .status-offline {
            background-color: #f56565;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .api-endpoints {
            grid-column: 1 / -1;
        }

        .endpoint-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        .endpoint {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .endpoint-method {
            font-weight: bold;
            color: #667eea;
            margin-right: 10px;
        }

        .endpoint-url {
            font-family: 'Courier New', monospace;
            color: #2d3748;
        }

        .endpoint-desc {
            font-size: 0.9rem;
            color: #718096;
            margin-top: 5px;
        }

        .footer {
            text-align: center;
            color: white;
            opacity: 0.8;
            margin-top: 40px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Grok 2 API</h1>
            <p> Grok 到 OpenAI API 格式的转换服务</p>
        </div>

        <div class="main-content">
            <div class="card screenshot-container">
                <h2>📸 Grok 状态截图</h2>
                <img src="/screenshots/grok-startup.png" alt="Grok 启动截图" class="screenshot" id="screenshot">
                <p style="color: #718096; font-size: 0.9rem;">最后更新: <span id="lastUpdate">加载中...</span></p>
            </div>

            <div class="card">
                <h2>⚙️ 基础配置</h2>
                <div class="config-item">
                    <span class="config-label">服务状态</span>
                    <span class="config-value">
                        <span class="status-indicator status-online"></span>
                        运行中
                    </span>
                </div>
                <div class="config-item">
                    <span class="config-label">服务端口</span>
                    <span class="config-value" id="serverPort">加载中...</span>
                </div>
                <div class="config-item">
                    <span class="config-label">Grok URL</span>
                    <span class="config-value" id="grokUrl">加载中...</span>
                </div>
                <div class="config-item">
                    <span class="config-label">默认模型</span>
                    <span class="config-value" id="defaultModel">加载中...</span>
                </div>
                <div class="config-item">
                    <span class="config-label">浏览器模式</span>
                    <span class="config-value" id="browserMode">加载中...</span>
                </div>
                <div class="config-item">
                    <span class="config-label">页面超时</span>
                    <span class="config-value" id="pageTimeout">加载中...</span>
                </div>
            </div>

            <div class="card api-endpoints">
                <h2>🔗 API 端点</h2>
                <div class="endpoint-list">
                    <div class="endpoint">
                        <div>
                            <span class="endpoint-method">GET</span>
                            <span class="endpoint-url">/health</span>
                        </div>
                        <div class="endpoint-desc">健康检查端点</div>
                    </div>
                    <div class="endpoint">
                        <div>
                            <span class="endpoint-method">GET</span>
                            <span class="endpoint-url">/v1/models</span>
                        </div>
                        <div class="endpoint-desc">获取可用模型列表</div>
                    </div>
                    <div class="endpoint">
                        <div>
                            <span class="endpoint-method">GET</span>
                            <span class="endpoint-url">/v1/models/{model}</span>
                        </div>
                        <div class="endpoint-desc">获取特定模型信息</div>
                    </div>
                    <div class="endpoint">
                        <div>
                            <span class="endpoint-method">POST</span>
                            <span class="endpoint-url">/v1/chat/completions</span>
                        </div>
                        <div class="endpoint-desc">聊天完成 API (兼容 OpenAI 格式)</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2024 Grok 2 API - 基于 H3 v2 构建</p>
        </div>
    </div>

    <script>
        // 获取配置信息
        async function loadConfig() {
            try {
                // 从健康检查端点获取基本信息
                const healthResponse = await fetch('/health');
                const healthData = await healthResponse.json();

                // 更新页面信息
                document.getElementById('serverPort').textContent = window.location.port || '7860';
                document.getElementById('grokUrl').textContent = 'https://grok.com';
                document.getElementById('defaultModel').textContent = 'grok3';
                document.getElementById('browserMode').textContent = 'Headless';
                document.getElementById('pageTimeout').textContent = '30000ms';

                // 更新截图时间戳
                updateScreenshotTimestamp();

            } catch (err) {
                console.error('加载配置失败:', err);
                // 设置默认值
                document.getElementById('serverPort').textContent = window.location.port || '7860';
                document.getElementById('grokUrl').textContent = 'https://grok.com';
                document.getElementById('defaultModel').textContent = 'grok3';
                document.getElementById('browserMode').textContent = 'Headless';
                document.getElementById('pageTimeout').textContent = '30000ms';
            }
        }

        // 更新截图时间戳
        function updateScreenshotTimestamp() {
            const now = new Date();
            const timestamp = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('lastUpdate').textContent = timestamp;
        }

        // 处理截图加载错误
        document.getElementById('screenshot').onerror = function() {
            this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaIquWbvuS4jeWPr+eUqDwvdGV4dD48L3N2Zz4=';
            this.alt = '截图不可用';
        };

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();

            // 每30秒刷新一次截图时间戳
            setInterval(updateScreenshotTimestamp, 30000);
        });
    </script>
</body>
</html>