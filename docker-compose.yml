services:
  lmarena2api:
    image: zhezzma/lmarena2api
    container_name: lmarena2api-app
    restart: unless-stopped
    ports:
      - "5210:7860"

    environment:
      # 必需的环境变量
      - BROWSER_TYPE=${BROWSER_TYPE:-patchright}
      - API_TOKEN=${API_TOKEN:-**********}

      # 服务器配置
      - PORT=7860
      - NODE_ENV=production
      
      # 浏览器配置
      - HEADLESS=true
       
      # 容器内的 HOME 目录
      - HOME=/home/<USER>
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 资源限制（可选）
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

