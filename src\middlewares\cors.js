import { handleCors,appendCorsPreflightHeaders, appendCorsHeaders, isPreflightRequest } from 'h3';



const corsOptions = {
  origin: process.env.CORS_ORIGIN || '*',
  methods: "*",
  allowedHeaders: "*",
  exposeHeaders: "*",
  credentials: false,
  maxAge: 86400, // 24 hours
  preflight: {
    statusCode: 204
  }
};
/**
 * CORS (跨域资源共享) 中间件。
 * 使用 H3 内置的 CORS 处理功能。
 * @param {import('h3').H3Event} event - H3 事件对象。
 * @param {import('h3').next} next - 下一个中间件函数。
 * @returns {Response|void} 如果是 OPTIONS 预检请求，则返回处理结果。
 */

export async function corsMiddleware(event, next) {
  const corsRes = handleCors(event, corsOptions);


  if (isPreflightRequest(event)) {

    return corsRes;
  }

  if (corsRes) {
    return corsRes;
  }
  const rawBody = await next();
  // [intercept response]
  return rawBody;
}

/**
 * 为流式响应 (Server-Sent Events) 设置特定的响应头。
 * @param {import('h3').H3Event} event - H3 事件对象。
 */
export function setStreamHeaders(event) {
  event.res.headers.set('Content-Type', 'text/event-stream');
  event.res.headers.set('Cache-Control', 'no-cache');
  event.res.headers.set('Connection', 'keep-alive');

  // 使用 H3 的 appendCorsHeaders 为流式响应添加 CORS 头
  //appendCorsHeaders(event, corsOptions);
}