import fs from 'node:fs';


const LogLevel = {
  DEBUG: 'DEBUG',
  INFO: 'INFO',
  WARN: 'WARN',
  ERROR: 'ERROR'
};

// 日志级别优先级映射
const LogLevelPriority = {
  [LogLevel.DEBUG]: 0,
  [LogLevel.INFO]: 1,
  [LogLevel.WARN]: 2,
  [LogLevel.ERROR]: 3
};

const LOG_CONFIG = {
  logFile: './logs/app.log',
  logDir: './logs',
  enableConsole: true,
  enableFile: true,
  logLevel: process.env.LOG_LEVEL || LogLevel.ERROR
};

/**
 * 检查指定的日志级别是否应该被记录。
 * @param {string} level - 要检查的日志级别。
 * @returns {boolean} 如果应该记录则返回 true，否则返回 false。
 */
function shouldLog(level) {
  const currentLevelPriority = LogLevelPriority[LOG_CONFIG.logLevel];
  const messageLevelPriority = LogLevelPriority[level];

  // 如果级别未定义，默认允许记录
  if (currentLevelPriority === undefined || messageLevelPriority === undefined) {
    return true;
  }

  // 只有当消息级别优先级大于等于配置级别时才记录
  return messageLevelPriority >= currentLevelPriority;
}

/**
 * 确保日志目录存在。
 */
function ensureLogDirectory() {
  if (!fs.existsSync(LOG_CONFIG.logDir)) {
    fs.mkdirSync(LOG_CONFIG.logDir, { recursive: true });
  }
}

/**
 * 格式化日志消息。
 * @param {any[]} args - 要记录的参数。
 * @returns {string} 格式化后的消息字符串。
 */
function formatMessage(...args) {
  return args.map(arg =>
    (typeof arg === 'object' && arg !== null) ? JSON.stringify(arg, null, 2) : String(arg)
  ).join(' ');
}

/**
 * 将日志异步写入文件。
 * @param {string} level - 日志级别。
 * @param {string} message - 日志消息。
 */
async function writeToFile(level, message) {
  if (!LOG_CONFIG.enableFile) return;
  ensureLogDirectory();
  const timestamp = new Date().toISOString();
  const logEntry = `[${timestamp}] [${level}] ${message}\n`;
  try {
    await fs.promises.appendFile(LOG_CONFIG.logFile, logEntry);
  } catch (err) {
    console.error('写入日志文件失败:', err);
  }
}

/**
 * 将日志输出到控制台。
 * @param {string} level - 日志级别。
 * @param {any[]} args - 要记录的参数。
 */
function writeToConsole(level, ...args) {
  if (!LOG_CONFIG.enableConsole) return;
  const consoleMethod = {
    [LogLevel.DEBUG]: console.debug,
    [LogLevel.INFO]: console.log,
    [LogLevel.WARN]: console.warn,
    [LogLevel.ERROR]: console.error,
  }[level] || console.log;

  const timestamp = new Date().toLocaleTimeString();
  consoleMethod(`[${timestamp}] [${level}]`, ...args);
}

/**
 * 通用日志记录函数。
 * @param {string} level - 日志级别。
 * @param {any[]} args - 要记录的参数。
 */
function log(level, ...args) {
  // 检查是否应该记录此级别的日志
  if (!shouldLog(level)) {
    return;
  }

  const message = formatMessage(...args);
  writeToConsole(level, ...args);
  writeToFile(level, message);
}

export const debug = (...args) => log(LogLevel.DEBUG, ...args);
export const info = (...args) => log(LogLevel.INFO, ...args);
export const warn = (...args) => log(LogLevel.WARN, ...args);
export const error = (...args) => log(LogLevel.ERROR, ...args);

// 导出日志级别常量，供其他模块使用
export default { debug, info, warn, error, LogLevel };