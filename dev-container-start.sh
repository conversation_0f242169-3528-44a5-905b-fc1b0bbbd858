#!/bin/bash

# 设置环境变量
export DISPLAY=:99
export XVFB_WHD=1920x1080x24

# 清理可能存在的旧进程
pkill -f Xvfb || true
pkill -f x11vnc || true
pkill -f websockify || true
pkill -f fluxbox || true

# 等待清理完成
sleep 2

echo "🚀 启动容器内 noVNC 开发环境..."

# 启动 Xvfb (虚拟显示服务器)
echo "启动 Xvfb 虚拟显示服务器..."
Xvfb :99 -screen 0 $XVFB_WHD -ac +extension GLX +render -noreset &
XVFB_PID=$!

# 等待 Xvfb 启动
sleep 3

# 验证 Xvfb 是否成功启动
if ! xdpyinfo -display :99 >/dev/null 2>&1; then
    echo "❌ Xvfb 启动失败，尝试重新启动..."
    sleep 2
    if ! xdpyinfo -display :99 >/dev/null 2>&1; then
        echo "❌ Xvfb 仍然无法启动，退出..."
        exit 1
    fi
fi
echo "✅ Xvfb 启动成功"

# 启动窗口管理器 (fluxbox)
echo "启动窗口管理器..."
fluxbox &
FLUXBOX_PID=$!

# 启动 VNC 服务器
echo "启动 VNC 服务器..."
# 使用更稳定的 VNC 配置，强制指定分辨率并禁用可能导致问题的功能
x11vnc -display :99 -nopw -listen 0.0.0.0 -xkb \
    -geometry 1920x1080 \
    -forever -shared \
    -noxdamage -noxfixes \
    -nowireframe -noscrollcopyrect \
    -quiet &
VNC_PID=$!

# 等待 VNC 服务器启动
sleep 2

# 启动 websockify (WebSocket 到 VNC 的代理)
echo "启动 websockify..."
# 添加 --heartbeat 参数以保持连接活跃
websockify --web=/opt/noVNC --heartbeat=30 6080 localhost:5900 &
WEBSOCKIFY_PID=$!

# 等待 websockify 启动
sleep 2

# 设置信号处理函数，确保优雅关闭
cleanup() {
    echo "正在关闭服务..."
    
    # 关闭 Node.js 应用
    if [ ! -z "$NODE_PID" ]; then
        kill $NODE_PID 2>/dev/null
        wait $NODE_PID 2>/dev/null
    fi
    
    # 关闭 websockify
    if [ ! -z "$WEBSOCKIFY_PID" ]; then
        kill $WEBSOCKIFY_PID 2>/dev/null
    fi
    
    # 关闭 VNC 服务器
    if [ ! -z "$VNC_PID" ]; then
        kill $VNC_PID 2>/dev/null
    fi
    
    # 关闭窗口管理器
    if [ ! -z "$FLUXBOX_PID" ]; then
        kill $FLUXBOX_PID 2>/dev/null
    fi
    
    # 关闭 Xvfb
    if [ ! -z "$XVFB_PID" ]; then
        kill $XVFB_PID 2>/dev/null
    fi
    
    echo "所有服务已关闭"
    exit 0
}

# 注册信号处理
trap cleanup SIGTERM SIGINT

# 检查 dev-container-local.sh 是否存在，如果存在则执行
if [ -f "dev-container-local.sh" ]; then
    echo "🔧 发现 dev-container-local.sh，正在执行..."
    ./dev-container-local.sh &
    echo "✅ dev-container-local.sh 执行完成"
fi

echo "🌐 noVNC Web 界面已启动！"
echo "📱 通过浏览器访问: http://localhost:6080"
echo "🚀 应用服务将在: http://localhost:7860"
echo "💡 提示: 在 VSCode 中转发端口 6080 和 7860"
echo ""
echo "现在启动应用程序..."

# 启动应用程序
npm run dev &
NODE_PID=$!

# 等待应用程序结束
wait $NODE_PID



# 如果应用程序意外退出，执行清理
cleanup
