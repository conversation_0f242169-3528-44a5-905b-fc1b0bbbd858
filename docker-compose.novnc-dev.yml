# Docker Compose noVNC 开发配置文件
# 用于启用 noVNC Web 界面远程查看浏览器界面 (开发模式)
# 使用方法: docker-compose -f docker-compose.yml -f docker-compose.override.yml -f docker-compose.novnc-dev.yml up -d

services:
  lmarena2api:
    # 启用 noVNC 端口映射
    ports:
      - "6080:6080"  # noVNC Web 界面端口
      - "5900:5900"  # VNC 直连端口

    # noVNC 相关环境变量
    environment:
      - ENABLE_NOVNC=true
      - NODE_ENV=development

    # 挂载修改后的启动脚本
    volumes:
      - "./start-with-xvfb-novnc-dev.sh:/app/start-with-xvfb-novnc-dev.sh"

    # 修改启动脚本以启用 noVNC (开发模式)
    command: ["/app/start-with-xvfb-novnc-dev.sh"]
