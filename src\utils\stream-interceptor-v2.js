// file: utils/stream-interceptor-v2.js

import { info, error } from './logger.js';

/**
 * V2: Uses <PERSON>wright's page.route() for robust, backend-only network interception.
 */
export class StreamInterceptorV2 {
  /**
   * The pattern to intercept. Using a RegExp is more robust than a glob pattern.
   * This will match any URL that contains "MakerSuiteService/GenerateContent".
   * @readonly
   */
  TARGET_URL_PATTERN = new RegExp('rest/app-chat/conversations/new');

  /**
   * @param {import('playwright').Page} page Playwright 页面对象
   * @param {function(string): void} onStreamChunk 流数据块回调
   * @param {function(): void} onStreamEnd 流结束回调
   */
  constructor(page, onStreamChunk, onStreamEnd) {
    this.page = page;
    this.onStreamChunk = onStreamChunk;
    this.onStreamEnd = onStreamEnd;
    this.isActive = false;
    this.routeHandler = this._handleRoute.bind(this);
  }

  async activate() {
    if (this.isActive) return;
    info('[InterceptorV2] 激活网络路由拦截...');

    // (Optional) Uncomment this block to log all requests for debugging purposes.
    /*
    this.page.on('request', request => {
      if (this.TARGET_URL_PATTERN.test(request.url())) {
        console.log('✅ [InterceptorV2] MATCHED:', request.method(), request.url());
      } else {
        // console.log('>> Request:', request.method(), request.url());
      }
    });
    */

    await this.page.route(this.TARGET_URL_PATTERN, this.routeHandler);
    this.isActive = true;
    info(`[InterceptorV2] 拦截器已激活，监听 URL pattern: ${this.TARGET_URL_PATTERN}`);
  }

  async deactivate() {
    if (!this.isActive) return;
    info('[InterceptorV2] 停用网络路由拦截...');
    try {
      await this.page.unroute(this.TARGET_URL_PATTERN, this.routeHandler);
      this.isActive = false;
      info('[InterceptorV2] 拦截器已停用。');
    } catch (err) {
      error(`[InterceptorV2] 停用拦截器时出错: ${err.message}`);
    }
  }

  async _handleRoute(route, request) {
    info(`[InterceptorV2] 拦截到目标请求: ${request.method()} ${request.url()}`);

    try {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ message: "Request handled by backend interceptor." })
      });
      info('[InterceptorV2] 已向浏览器发送伪造的成功响应。');
    } catch (err) {
      error(`[InterceptorV2] Fulfill 原始请求失败: ${err.message}`);
      if (route.isFulfillable()) {
        await route.abort().catch(e => error(`[InterceptorV2] Abort 请求也失败: ${e.message}`));
      }
      return;
    }

    try {
      info('[InterceptorV2] 正在使用 Node.js fetch 重新发送请求...');
      const headers = await request.headers();
      delete headers['x-playwright-api-request'];
      delete headers['content-length'];


      const response = await fetch(request.url(), {
        method: request.method(),
        headers: headers,
        body: request.postDataBuffer(),
      });

      if (!response.ok) {
        const errorText = await response.text();
        this.onStreamChunk(errorText);
        return;
      }

      if (!response.body) {
        throw new Error('后端 fetch 响应中没有 body。');
      }

      info('[InterceptorV2] 开始从后端 fetch 接收流式数据...');
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }
        const chunk = decoder.decode(value, { stream: true });
        this.onStreamChunk(chunk);
      }

      info('[InterceptorV2] 流接收完毕。');
      this.onStreamEnd();

    } catch (err) {
      error('[InterceptorV2] 后端处理请求时发生严重错误:', err);
      this.onStreamEnd();
    }
  }
}