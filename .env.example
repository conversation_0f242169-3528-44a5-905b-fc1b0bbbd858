# 服务器配置
PORT=3000
NODE_ENV=development

# 浏览器配置
HEADLESS=false
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36

# Grok 配置
WEB_URL=https://grok.com/
PAGE_TIMEOUT=30000
DEFAULT_MODEL=grok3

# CORS 配置
CORS_ORIGIN=*

# API 认证配置
# 设置一个强密码作为API访问令牌
API_TOKEN=your_secret_api_token_here

# SSO 认证配置
# 如果设置了 USE_APP_TOKEN，则跳过 API_TOKEN 认证，改为使用 SSO 认证
# 在此模式下，每个请求的 Authorization 头中的 Bearer token 将被视为 SSO token
USE_APP_TOKEN=true

# 人类行为模拟配置
# 是否启用人类行为模拟（随机点击等），有助于避免被检测为自动化工具
ENABLE_HUMAN_SIMULATION=false

# 页面池配置
# 是否禁用页面池（设置为 true 时，每次使用完页面后直接关闭，不复用）
# 禁用页面池可以减少内存使用，但会增加页面创建的开销
DISABLE_PAGE_POOL=false

# 日志配置
# 日志级别：DEBUG, INFO, WARN, ERROR（默认：ERROR）
# DEBUG: 显示所有日志
# INFO: 显示 INFO、WARN、ERROR 级别的日志
# WARN: 显示 WARN、ERROR 级别的日志
# ERROR: 只显示 ERROR 级别的日志
LOG_LEVEL=ERROR
