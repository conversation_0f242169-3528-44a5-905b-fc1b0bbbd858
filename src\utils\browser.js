import { chromium, firefox } from 'playwright'
import { chromium as patchChromium } from 'patchright'; //patchright注入脚本的时候注意第三个参数设置false
import { launchOptions, Camoufox } from 'camoufox-js';
import { newInjectedContext } from 'fingerprint-injector';
import { FingerprintGenerator } from 'fingerprint-generator';
import config from '../config.js'
import { loadCookies } from './common-utils.js'
import { info, warn, error } from './logger.js'


// 存储浏览器实例
let browser = null
// 全局浏览器环境管理器实例
let browserEnvManager = null


export async function createBrowser() {
  let browser = null;
  const browserType = config.browser.type;
  info(`正在启动浏览器类型: ${browserType}`);


  if (browserType === 'camoufox') {
    // // 使用 camoufox (基于 firefox)
    // const camoufoxOptions = await launchOptions({
    //   headless: config.browser.headless,
    //   args: config.browser.args
    // });
    // // 合并配置，优先使用camoufox的配置
    // browser = await firefox.launch({
    //   ...camoufoxOptions,
    //   timeout: config.browser.timeout,
    //   executablePath: config.browser.executablePath || camoufoxOptions.executablePath //必须要有camoufoxOptions.executablePath 如果是undefined,则使用firefox
    // });
    browser = await Camoufox({
      headless: config.browser.headless,
      args: config.browser.args
    });
  }
  else if (browserType === 'firefox') {
    // 使用 firefox
    browser = await firefox.launch({
      headless: config.browser.headless,
      timeout: config.browser.timeout,
      args: config.browser.args,
      executablePath: config.browser.executablePath
    });
  }
  else if (browserType === 'patchright') {


    // const chromium = addExtra(patchChromium)
    // chromium.use(StealthPlugin())
    // 使用 patchright
    browser = await patchChromium.launch({
      headless: config.browser.headless,
      timeout: config.browser.timeout,
      args: config.browser.args,
      executablePath: config.browser.executablePath
    });
  }
  else {
    // 使用 chromium
    // const chromiumEx = addExtra(chromium)
    // chromiumEx.use(StealthPlugin())
    browser = await chromium.launch({
      headless: config.browser.headless,
      timeout: config.browser.timeout,
      args: config.browser.args,
      executablePath: config.browser.executablePath
    });
  }

  return browser;
}



/**
 * Patchright 兼容的 page.evaluate 包装函数
 * @param {import('playwright').Page} page
 * @param {Function|string} pageFunction
 * @param {any} arg
 * @param {boolean} isolatedContext - 是否使用隔离上下文（仅在 Patchright 模式下有效）
 * @returns {Promise<any>}
 */
export async function compatEvaluate(page, pageFunction, arg, isolatedContext = null) {
  if (config.browser.type === 'patchright') {
    // Patchright 模式：根据 Patchright 的 API，isolatedContext 是第四个参数
    const useIsolated = isolatedContext !== null ? isolatedContext : false;
    return await page.evaluate(pageFunction, arg, useIsolated);
  } else {
    // 标准模式：只传递 pageFunction 和 arg，忽略 isolatedContext
    if (arg !== undefined) {
      return await page.evaluate(pageFunction, arg);
    } else {
      return await page.evaluate(pageFunction);
    }
  }
}


export async function createBrowserContext(browser, options = {}) {
  //不要使用指纹..会影响patch
  if (config.browser.type === 'patchright') {
    return browser.newContext(options);
  }
  // 创建新的 context
  const fingerprint = new FingerprintGenerator().getFingerprint({
    devices: ["desktop"],
    //operatingSystems: ["windows"],
    browsers: [{ name: config.browser.type === 'chromium' ? 'chrome' : 'firefox' }],
    // screen: {
    //   minWidth: 1024,
    //   maxWidth: 1024,
    //   minHeight: 768,
    //   maxHeight: 768,
    // }
  })

  const newContext = await newInjectedContext(browser, {
    // Constraints for the generated fingerprint (optional)
    fingerprint: fingerprint,
    // Playwright's newContext() options (optional, random example for illustration)
    newContextOptions: {
      locale: 'zh-CN',
      serviceWorkers: 'allow', // 允许Service Workers (有助于缓存)
      javaScriptEnabled: true,
      bypassCSP: false, // 绕过内容安全策略
      ...options
    },
  });
  return newContext;
}

// 用户浏览器环境管理（包含 context 和页面池）
class UserBrowserEnvironment {
  constructor(appToken, maxSize = 5) {
    this.appToken = appToken
    this.maxSize = maxSize
    this.availablePages = []
    this.busyPages = new Set()
    this.totalPages = 0
    this.context = null // 用户专属的浏览器上下文
    // 检查是否禁用页面池（通过配置控制）
    this.usePagePool = !config.disablePagePool
    // 检查是否每次都创建新的上下文
    this.alwaysNewContext = config.alwaysNewContext
  }


  /**
 * 创建新的浏览器上下文
 * @returns {Promise<any>} 新创建的浏览器上下文
 */
  async createNewContext() {
    if (!browser) {
      browser = await createBrowser()
    }
    const newContext = await createBrowserContext(browser);
    return newContext
  }
  /**
   * 初始化用户的浏览器上下文
   * @returns {Promise<void>}
   */
  async initContext() {
    // 如果启用了每次都创建新上下文，先关闭现有上下文
    if (this.alwaysNewContext && this.context) {
      try {
        await this.context.close();
        info(`已关闭旧的浏览器上下文 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})`);
      } catch (err) {
        error(`关闭旧上下文时出错: ${err.message}`);
      }
      this.context = null;
    }
    if (this.context && !this.alwaysNewContext) {
      return; // 已经初始化过了，且不需要每次创建新上下文
    }
    this.context = await this.createNewContext();
    if (this.appToken) {
      // 使用 appToken 构建 cookies
      const cookies = config.createCookiesByToken(this.appToken);
      info(`为用户创建${this.alwaysNewContext ? '新的' : ''}浏览器上下文，APP_TOKEN: ${this.appToken.substring(0, 10)}...`);
      await this.context.addCookies(cookies);
    } else {
      // 使用默认的 cookies 配置
      const existingCookies = loadCookies(config.cookieFile, config.cookiesFromEnv);
      info(`为默认用户创建${this.alwaysNewContext ? '新的' : ''}浏览器上下文`);
      if (existingCookies && existingCookies.length > 0) {
        await this.context.addCookies(existingCookies);
        info(`已加载 ${existingCookies.length} 个现有 Cookie。`);
      }
    }
  }

  /**
   * 获取一个可用的页面
   * @returns {Promise<Page>}
   */
  async getPage() {
    // 确保 context 已初始化（如果启用了每次新上下文，这里会创建新的上下文）
    await this.initContext();

    // 如果禁用了页面池或启用了每次新上下文，直接创建新页面
    if (!this.usePagePool || this.alwaysNewContext) {
      const page = await this.context.newPage()
      const mode = this.alwaysNewContext ? '新上下文模式' : '无池模式';
      info(`创建新页面 (${mode}) (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})`)
      return page
    }

    // 以下是原有的页面池逻辑
    // 如果有可用页面，直接返回
    if (this.availablePages.length > 0) {
      const page = this.availablePages.pop()
      this.busyPages.add(page)
      info(`从用户浏览器环境获取页面 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})，当前忙碌页面数: ${this.busyPages.size}`)
      return page
    }

    // 如果没有可用页面且未达到最大数量，创建新页面
    if (this.totalPages < this.maxSize) {
      const page = await this.context.newPage()
      this.busyPages.add(page)
      this.totalPages++
      info(`为用户创建新页面 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})，总页面数: ${this.totalPages}，忙碌页面数: ${this.busyPages.size}`)
      return page
    }

    // 如果达到最大数量，等待有页面释放
    info(`用户浏览器环境页面池已满 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})，等待页面释放...`)
    return new Promise((resolve) => {
      const checkAvailable = () => {
        if (this.availablePages.length > 0) {
          const page = this.availablePages.pop()
          this.busyPages.add(page)
          info(`等待后获取到页面 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})，当前忙碌页面数: ${this.busyPages.size}`)
          resolve(page)
        } else {
          setTimeout(checkAvailable, 100)
        }
      }
      checkAvailable()
    })
  }

  /**
   * 释放页面回到池中或直接关闭（取决于是否使用页面池）
   * @param {Page} page
   */
  async releasePage(page) {
    // 如果禁用了页面池或启用了每次新上下文，直接关闭页面
    if (!this.usePagePool || this.alwaysNewContext) {
      try {
        await page.close()
        const mode = this.alwaysNewContext ? '新上下文模式' : '无池模式';
        info(`页面已关闭 (${mode}) (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})`)

        // 如果启用了每次新上下文，也关闭上下文
        if (this.alwaysNewContext && this.context) {
          try {
            await this.context.close()
            info(`上下文已关闭 (新上下文模式) (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})`)
            this.context = null
          } catch (contextErr) {
            error(`关闭上下文时出错: ${contextErr.message}`)
          }
        }
      } catch (err) {
        error(`关闭页面时出错: ${err.message}`)
      }
      return
    }

    // 以下是原有的页面池逻辑
    if (!this.busyPages.has(page)) {
      info(`尝试释放不在忙碌列表中的页面 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})`)
      return
    }

    try {
      // 清理页面状态
      await this.cleanupPage(page)

      this.busyPages.delete(page)
      this.availablePages.push(page)
      info(`页面已释放回池中 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})，可用页面数: ${this.availablePages.length}，忙碌页面数: ${this.busyPages.size}`)
    } catch (err) {
      error(`清理页面时出错，将关闭该页面: ${err.message}`)
      await this.removePage(page)
    }
  }

  /**
   * 清理页面状态
   * @param {Page} page
   */
  async cleanupPage(page) {
    try {
      // 移除所有事件监听器
      page.removeAllListeners()

      // 清理流式拦截器相关的函数和状态
      try {
        await page.evaluate(() => {
          // 清理所有流式拦截器相关的函数
          const keys = Object.keys(window)
          keys.forEach(key => {
            if (key.startsWith('__handleStreamChunk') ||
              key.startsWith('__onStreamChunk') ||
              key.startsWith('__onStreamEnd')) {
              delete window[key]
            }
          })

          // 清理拦截器状态
          if (window.__streamInterceptor) {
            if (typeof window.__streamInterceptor.deactivate === 'function') {
              window.__streamInterceptor.deactivate()
            }
            delete window.__streamInterceptor
          }

          // 清理其他相关状态
          delete window.__handleStreamChunk
          delete window.__onStreamChunk
          delete window.__onStreamEnd
          delete window.__streamCallbacks
        })
      } catch (evalError) {
        // 忽略页面evaluate错误，可能页面已经关闭
        info(`清理页面状态时出现evaluate错误: ${evalError.message}`)
      }

      info(`页面状态已清理 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})`)
    } catch (err) {
      throw new Error(`清理页面失败: ${err.message}`)
    }
  }

  /**
   * 从池中移除页面
   * @param {Page} page
   */
  async removePage(page) {
    try {
      this.busyPages.delete(page)
      const index = this.availablePages.indexOf(page)
      if (index > -1) {
        this.availablePages.splice(index, 1)
      }

      await page.close()
      this.totalPages--
      info(`页面已从池中移除 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})，总页面数: ${this.totalPages}`)
    } catch (err) {
      error(`关闭页面时出错: ${err.message}`)
    }
  }

  /**
   * 清理所有页面和上下文
   */
  async cleanup() {
    info(`开始清理用户浏览器环境 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})...`)

    // 关闭所有忙碌页面
    for (const page of this.busyPages) {
      try {
        await page.close()
      } catch (err) {
        error(`关闭忙碌页面时出错: ${err.message}`)
      }
    }

    // 关闭所有可用页面
    for (const page of this.availablePages) {
      try {
        await page.close()
      } catch (err) {
        error(`关闭可用页面时出错: ${err.message}`)
      }
    }

    // 关闭用户上下文
    if (this.context) {
      try {
        await this.context.close()
        info(`用户上下文已关闭 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})`)
      } catch (err) {
        error(`关闭用户上下文时出错: ${err.message}`)
      }
    }

    this.busyPages.clear()
    this.availablePages = []
    this.totalPages = 0
    this.context = null
    info(`用户浏览器环境清理完成 (APP_TOKEN: ${this.appToken ? this.appToken.substring(0, 10) + '...' : 'default'})`)
  }

  /**
   * 获取池状态信息
   */
  getStatus() {
    return {
      appToken: this.appToken,
      total: this.totalPages,
      available: this.availablePages.length,
      busy: this.busyPages.size,
      maxSize: this.maxSize
    }
  }
}

// 多用户浏览器环境管理器
class BrowserEnvironmentManager {
  constructor(defaultMaxSize = 5) {
    this.defaultMaxSize = defaultMaxSize
    this.userEnvironments = new Map() // appToken -> UserBrowserEnvironment
  }

  /**
   * 获取或创建用户浏览器环境
   * @param {string|null} appToken
   * @returns {UserBrowserEnvironment}
   */
  getUserEnvironment(appToken = null) {
    const envKey = appToken || 'default'

    if (!this.userEnvironments.has(envKey)) {
      const userEnv = new UserBrowserEnvironment(appToken, this.defaultMaxSize)
      this.userEnvironments.set(envKey, userEnv)
      info(`为用户创建新的浏览器环境 (APP_TOKEN: ${appToken ? appToken.substring(0, 10) + '...' : 'default'})`)
    }

    return this.userEnvironments.get(envKey)
  }

  /**
   * 获取页面
   * @param {string|null} appToken
   * @returns {Promise<Page>}
   */
  async getPage(appToken = null) {
    const userEnv = this.getUserEnvironment(appToken)
    return await userEnv.getPage()
  }

  /**
   * 释放页面
   * @param {Page} page
   * @param {string|null} appToken
   */
  async releasePage(page, appToken = null) {
    const userEnv = this.getUserEnvironment(appToken)
    await userEnv.releasePage(page)
  }

  /**
   * 清理所有用户的浏览器环境
   */
  async cleanup() {
    info('开始清理所有用户浏览器环境...')

    for (const [envKey, userEnv] of this.userEnvironments) {
      try {
        await userEnv.cleanup()
      } catch (err) {
        error(`清理用户浏览器环境失败 (${envKey}): ${err.message}`)
      }
    }

    this.userEnvironments.clear()
    info('所有用户浏览器环境清理完成')
  }

  /**
   * 获取所有用户浏览器环境状态
   */
  getAllStatus() {
    const status = {}
    for (const [envKey, userEnv] of this.userEnvironments) {
      status[envKey] = userEnv.getStatus()
    }
    return status
  }
}





/**
 * 初始化浏览器（保留用于向后兼容，但现在推荐使用 UserBrowserEnvironment）
 * @param {string|null} appToken - APP_TOKEN token，用于多用户支持
 * @returns {Promise<{browser: any, context: any}>}
 */
export async function initBrowserContext(appToken = null) {
  // 现在通过浏览器环境管理器获取用户环境
  const manager = getBrowserEnvironmentManager()
  const userEnv = manager.getUserEnvironment(appToken)
  await userEnv.initContext()

  return { browser, context: userEnv.context }
}

/**
 * 初始化浏览器环境管理器
 * @param {number} maxSize - 每个用户浏览器环境的最大页面数
 * @returns {BrowserEnvironmentManager}
 */
export function initPagePool(maxSize = 5) {
  if (!browserEnvManager) {
    browserEnvManager = new BrowserEnvironmentManager(maxSize)
    info(`浏览器环境管理器已初始化，每个用户最大页面数: ${maxSize}`)
  }
  return browserEnvManager
}

/**
 * 获取浏览器环境管理器实例
 * @returns {BrowserEnvironmentManager}
 */
export function getBrowserEnvironmentManager() {
  if (!browserEnvManager) {
    browserEnvManager = new BrowserEnvironmentManager()
    info('浏览器环境管理器已自动初始化，使用默认配置')
  }
  return browserEnvManager
}

/**
 * 从用户浏览器环境获取页面
 * @param {string|null} appToken - APP_TOKEN token，用于多用户支持
 * @returns {Promise<any>}
 */
export async function getPageFromPool(appToken = null) {
  const manager = getBrowserEnvironmentManager()

  // 检查是否为 V4 模式
  if (process.env.USE_INTERCEPTOR_V4 === 'true') {
    // V4 模式：简化页面获取逻辑
    const userEnv = manager.getUserEnvironment(appToken)

    // 确保 context 已初始化
    await userEnv.initContext()

    // 如果有任何页面（包括正在使用的），直接返回第一个
    if (userEnv.availablePages.length > 0) {
      const page = userEnv.availablePages[0]
      info(`[V4模式] 从可用页面中获取页面`)
      return page
    }

    if (userEnv.busyPages.size > 0) {
      const page = Array.from(userEnv.busyPages)[0]
      info(`[V4模式] 从忙碌页面中获取页面`)
      return page
    }
  }

  // 传统模式：使用完整的页面池逻辑
  const page = await manager.getPage(appToken)

  // 将页面切换到前台
  await page.bringToFront()

  return page
}

/**
 * 释放页面回到用户浏览器环境
 * @param {any} page
 * @param {string|null} appToken - APP_TOKEN token，用于多用户支持
 */
export async function releasePageToPool(page, appToken = null) {
  // 检查是否为 V4 模式
  if (process.env.USE_INTERCEPTOR_V4 === 'true') {
    // V4 模式：将页面放回池中，不关闭
    const manager = getBrowserEnvironmentManager()
    const userEnv = manager.getUserEnvironment(appToken)

    if (page && !page.isClosed()) {
      userEnv.availablePages.push(page)
      info(`[V4模式] 页面已放回池中，当前可用页面数: ${userEnv.availablePages.length}`)
    }
  } else {
    // 传统模式：使用完整的页面池逻辑
    const manager = getBrowserEnvironmentManager()
    await manager.releasePage(page, appToken)
  }
}

/**
 * 获取所有用户浏览器环境状态
 * @returns {Object}
 */
export function getPagePoolStatus() {
  if (!browserEnvManager) {
    return {}
  }
  return browserEnvManager.getAllStatus()
}




/**
 * 智能导航到指定URL，如果页面已在目标URL则进行刷新
 * @param {Page} page - Playwright页面对象
 * @param {string} targetUrl - 目标URL
 * @param {Object} options - 导航选项
 * @returns {Promise<boolean>} 是否进行了实际导航
 */
export async function smartNavigate(page, targetUrl, options = {}) {
  const currentUrl = page.url()
  info('当前页面URL:', currentUrl)
  info('目标URL:', targetUrl)


  try {
    if (currentUrl !== targetUrl) {
      info('页面URL不匹配，需要导航...')
      await page.goto(targetUrl, {
        waitUntil: 'load',
        timeout: 30000,
        ...options
      })
      info('页面导航完成')
      return true
    } else {
      info('页面已在目标URL，进行刷新以确保最新状态...')
      // 确保页面处于活跃状态
      await page.bringToFront()
      await page.goto(targetUrl.split("?")[0], {
        waitUntil: 'load',
        timeout: 30000,
        ...options
      })
      await page.goto(targetUrl, {
        waitUntil: 'load',
        timeout: 30000,
        ...options
      })
      info('页面刷新完成')
      return true
    }
  }
  catch {

  }
  return false
}



/**
 * 模拟人类随机点击行为 - 仅在输入框上方300像素内的安全区域
 * @param {Page} page - Playwright页面对象
 * @param {Object} options - 配置选项
 * @param {number} options.minClicks - 最少点击次数，默认2
 * @param {number} options.maxClicks - 最多点击次数，默认5
 * @param {number} options.minDelay - 点击间最小延迟（毫秒），默认500
 * @param {number} options.maxDelay - 点击间最大延迟（毫秒），默认2000
 * @param {Element} options.referenceElement - 参考元素（如输入框），在其上方300像素内进行点击
 * @returns {Promise<void>}
 */
export async function simulateHumanRandomClicks(page, options = {}) {
  const {
    minClicks = 1,
    maxClicks = 3,
    minDelay = 300,
    maxDelay = 500,
    referenceElement = null
  } = options

  try {
    info('开始模拟人类随机点击行为...')

    // 随机确定点击次数
    const clickCount = Math.floor(Math.random() * (maxClicks - minClicks + 1)) + minClicks
    info(`将进行 ${clickCount} 次随机点击`)

    let safeArea = null

    // 如果提供了参考元素，获取其位置信息
    if (referenceElement) {
      try {
        const boundingBox = await referenceElement.boundingBox()
        if (boundingBox) {
          // 定义输入框上方300像素内的安全区域
          safeArea = {
            x: boundingBox.x - 50, // 左边扩展50px
            y: Math.max(0, boundingBox.y - 200), // 上方300px区域
            width: boundingBox.width + 50, // 宽度扩展100px
            height: 300 // 高度300px的安全区域（输入框上方300像素内）
          }
          info(`使用输入框附近的安全区域: x=${safeArea.x}, y=${safeArea.y}, w=${safeArea.width}, h=${safeArea.height}`)
        }
      } catch (err) {
        error(`获取参考元素位置失败，使用默认安全区域: ${err.message}`)
      }
    }

    // 如果没有安全区域，使用页面中央的安全区域
    if (!safeArea) {
      const viewport = page.viewportSize()
      const width = viewport?.width || 1280
      const height = viewport?.height || 720

      safeArea = {
        x: width * 0.3,
        y: height * 0.3,
        width: width * 0.4,
        height: height * 0.2
      }
      info(`使用默认安全区域: x=${safeArea.x}, y=${safeArea.y}, w=${safeArea.width}, h=${safeArea.height}`)
    }

    for (let i = 0; i < clickCount; i++) {
      try {
        // 在安全区域内生成随机坐标
        const x = Math.floor(Math.random() * safeArea.width) + safeArea.x
        const y = Math.floor(Math.random() * safeArea.height) + safeArea.y

        info(`第 ${i + 1} 次安全点击: (${x}, ${y})`)

        // 检查点击位置是否有可交互元素
        const elementAtPoint = await page.locator(`*`).first().evaluate((_, coords) => {
          const element = document.elementFromPoint(coords.x, coords.y)
          if (!element) return { safe: true }

          const tagName = element.tagName.toLowerCase()
          const hasHref = element.hasAttribute('href')
          const hasOnClick = element.hasAttribute('onclick') || element.onclick
          const isButton = tagName === 'button' || element.type === 'button'
          const isLink = tagName === 'a' || hasHref
          const isInput = ['input', 'textarea', 'select'].includes(tagName)

          return {
            safe: !isButton && !isLink && !hasOnClick && !isInput,
            tagName,
            hasHref,
            hasOnClick,
            isButton,
            isLink,
            isInput
          }
        }, { x, y })

        if (elementAtPoint.safe) {
          // 模拟鼠标移动到目标位置
          await page.mouse.move(x, y, {
            steps: Math.floor(Math.random() * 10) + 5 // 5-15步移动，更自然
          })

          // 随机等待一小段时间
          await page.waitForTimeout(Math.floor(Math.random() * 200) + 100)

          // 执行点击
          await page.mouse.click(x, y)
          info(`安全点击完成: (${x}, ${y})`)
        } else {
          info(`跳过不安全的点击位置 (${x}, ${y}): ${elementAtPoint.tagName}`)
        }

        // 点击间随机延迟
        if (i < clickCount - 1) {
          const delay = Math.floor(Math.random() * (maxDelay - minDelay + 1)) + minDelay
          info(`等待 ${delay}ms 后进行下一次点击`)
          await page.waitForTimeout(delay)
        }
      } catch (clickError) {
        info(`第 ${i + 1} 次点击出现错误，继续下一次: ${clickError.message}`)
      }
    }

    info('安全随机点击模拟完成')
  } catch (err) {
    error(`模拟安全随机点击时出现错误: ${err.message}`)
  }
}

/**
 * 模拟更复杂的人类行为
 * @param {Page} page - Playwright页面对象
 * @param {Object} options - 配置选项
 * @returns {Promise<void>}
 */
export async function simulateHumanBehavior(page, options = {}) {
  const {
    includeScrolling = true,
    includeMouseMovement = true,
    includeRandomClicks = true,
    duration = 3000 // 总持续时间
  } = options

  try {
    info('开始模拟复杂的人类行为...')

    const startTime = Date.now()
    const viewport = page.viewportSize()
    const width = viewport?.width || 1280
    const height = viewport?.height || 720

    while (Date.now() - startTime < duration) {
      const action = Math.random()

      if (action < 0.3 && includeScrolling) {
        // 30% 概率进行滚动
        const scrollDirection = Math.random() > 0.5 ? 'down' : 'up'
        const scrollAmount = Math.floor(Math.random() * 300) + 100

        info(`模拟滚动: ${scrollDirection}, 距离: ${scrollAmount}px`)
        await page.mouse.wheel(0, scrollDirection === 'down' ? scrollAmount : -scrollAmount)

      } else if (action < 0.6 && includeMouseMovement) {
        // 30% 概率进行鼠标移动
        const x = Math.floor(Math.random() * width)
        const y = Math.floor(Math.random() * height)

        info(`模拟鼠标移动到: (${x}, ${y})`)
        await page.mouse.move(x, y, {
          steps: Math.floor(Math.random() * 15) + 5
        })

      } else if (action < 0.8 && includeRandomClicks) {
        // 20% 概率进行点击
        const x = Math.floor(Math.random() * (width * 0.8)) + width * 0.1
        const y = Math.floor(Math.random() * (height * 0.8)) + height * 0.1

        info(`模拟随机点击: (${x}, ${y})`)
        await page.mouse.click(x, y)
      }

      // 随机等待
      const waitTime = Math.floor(Math.random() * 800) + 200
      await page.waitForTimeout(waitTime)
    }

    info('复杂人类行为模拟完成')
  } catch (err) {
    error(`模拟复杂人类行为时出现错误: ${err.message}`)
  }
}

/**
 * 使用2captcha解决Cloudflare Turnstile挑战
 * @param {Object} turnstileData - Turnstile挑战数据
 * @param {number} maxRetries - 最大重试次数，默认30
 * @param {number} retryInterval - 重试间隔（毫秒），默认5000
 * @returns {Promise<string>} - 返回解决后的token
 */
async function getTwoCaptchaTurnstileToken(turnstileData, maxRetries = 30, retryInterval = 5000) {
  const twoCapchaKey = config.twoCaptchaKey;

  if (!twoCapchaKey) {
    throw new Error('2captcha API key not configured. Please set TWO_CAPTCHA_KEY environment variable.');
  }

  try {
    info('开始使用2captcha解决Turnstile挑战...');

    // Step 1: Create captcha task
    const createTaskResponse = await fetch('https://api.2captcha.com/createTask', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        clientKey: twoCapchaKey,
        task: {
          type: 'TurnstileTaskProxyless',
          websiteURL: turnstileData.websiteURL,
          websiteKey: turnstileData.websiteKey,
          data: turnstileData.data,
          pagedata: turnstileData.pagedata,
          action: turnstileData.action,
          userAgent: turnstileData.userAgent
        },
      })
    });

    const createTaskResult = await createTaskResponse.json();

    if (createTaskResult.errorId !== 0) {
      throw new Error(`Failed to create task: ${JSON.stringify(createTaskResult)}`);
    }

    const taskId = createTaskResult.taskId;
    info(`2captcha任务已创建，任务ID: ${taskId}`);

    // Step 2: Get task result (with retry logic)
    let retries = 0;

    while (retries < maxRetries) {
      // Wait for the specified interval
      await new Promise(resolve => setTimeout(resolve, retryInterval));

      const getResultResponse = await fetch('https://api.2captcha.com/getTaskResult', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          clientKey: twoCapchaKey,
          taskId: taskId,
        })
      });

      const getResultData = await getResultResponse.json();

      if (getResultData.errorId !== 0) {
        throw new Error(`Failed to get task result: ${JSON.stringify(getResultData)}`);
      }

      // Check if task is completed
      if (getResultData.status === 'ready') {
        info(`Turnstile挑战解决成功: ${JSON.stringify(getResultData)}`);
        // Return the captcha token
        return getResultData.solution.token;
      }

      // If task is still processing, increment retry count
      retries++;
      info(`等待2captcha解决Turnstile挑战... (${retries}/${maxRetries})`);
    }

    throw new Error(`Exceeded maximum retries (${maxRetries}), unable to get captcha result`);
  } catch (error) {
    throw new Error(`Turnstile solution failed: ${error.message}`);
  }
}

async function solveTurnstileChallenge(page, turnstileData) {

  // 页面日志监听器
  const consoleHandler = (msg) => {
    const type = msg.type();
    const text = msg.text();
    info(`[页面${type.toUpperCase()}] ${text}`);
  };

  const errorHandler = (error) => {
    error(`[页面错误] ${error.message}`);
  };

  const responseHandler = (response) => {
    info(`[页面响应] ${response.status()} ${response.url()}`);
  };

  try {
    info('开始处理页面初始化阻挡元素...');

    // 添加页面日志监听
    page.on('console', consoleHandler);
    page.on('pageerror', errorHandler);
    page.on('response', responseHandler);
    info('已添加页面日志监听器');

    // 首先在页面加载的最早阶段注入Turnstile拦截脚本
    await page.addInitScript(() => {
      // 在加载 Turnstile 小组件之前注入脚本
      if (!window.__turnstileInterceptorInjected) {
        window.__turnstileInterceptorInjected = true;

        let i;
        i = setInterval(() => {
          if (window.turnstile) {
            clearInterval(i);
            window.turnstile.render = (a, b) => {
              let p = {
                type: "TurnstileTaskProxyless",
                websiteKey: b.sitekey,
                websiteURL: window.location.href,
                data: b.cData,
                pagedata: b.chlPageData,
                action: b.action,
                userAgent: navigator.userAgent
              };
              window.turnstileParams = p;
              window.tsCallback = b.callback;
              return 'foo';
            };
          }
        }, 10);
      }
    });
    // 检测是否存在Cloudflare挑战页面
    const cloudflareDetected = await page.evaluate(() => {
      // 检查是否是Cloudflare挑战页面
      const indicators = [
        // Turnstile相关元素
        document.querySelectorAll('[data-sitekey], iframe[src*="turnstile"], .cf-turnstile').length > 0,
        // 通用Cloudflare挑战页面指示器
        document.title.includes('Just a moment'),
        document.title.includes('Checking your browser'),
        document.querySelector('.cf-error-title'),
        document.querySelector('#cf-error-details'),
        document.querySelector('.cf-browser-verification'),
        // 检查是否有Cloudflare挑战脚本
        document.querySelector('script[src*="/cdn-cgi/challenge-platform"]'),
        // 检查window._cf_chl_opt对象
        typeof window._cf_chl_opt !== 'undefined',
        // 检查页面内容
        document.body && document.body.innerHTML.includes('_cf_chl_opt')
      ];

      return {
        isCloudflare: indicators.some(indicator => indicator),
        isTurnstile: indicators[0], // 第一个指示器是Turnstile检测
        hasChallenge: indicators.slice(1).some(indicator => indicator)
      };
    });

    if (!cloudflareDetected.isCloudflare) {
      info('未检测到Cloudflare挑战');
      return false;
    }

    info(`检测到Cloudflare挑战 - Turnstile: ${cloudflareDetected.isTurnstile}, 其他挑战: ${cloudflareDetected.hasChallenge}`);

    // 只有检测到Turnstile挑战时才使用2captcha
    info('检测到Cloudflare Turnstile挑战，开始使用2captcha处理...');

    // 如果页面已经加载，需要再次注入脚本（因为addInitScript可能没有生效）
    await page.evaluate(() => {
      // 检查是否已经注入过脚本
      if (!window.__turnstileInterceptorInjected) {
        window.__turnstileInterceptorInjected = true;

        let i;
        i = setInterval(() => {
          if (window.turnstile) {
            clearInterval(i);
            window.turnstile.render = (a, b) => {
              let p = {
                type: "TurnstileTaskProxyless",
                websiteKey: b.sitekey,
                websiteURL: window.location.href,
                data: b.cData,
                pagedata: b.chlPageData,
                action: b.action,
                userAgent: navigator.userAgent
              };
              window.turnstileParams = p;
              window.tsCallback = b.callback;
              return 'foo';
            };
          }
        }, 10);
      }
    });



    let turnstileData;

    try {
      // 尝试获取Turnstile参数
      turnstileData = await page.evaluate(() => {
        return new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('获取Turnstile参数超时'));
          }, 10000);

          // 等待参数被设置
          const checkParams = setInterval(() => {
            if (window.turnstileParams) {
              clearInterval(checkParams);
              clearTimeout(timeout);
              resolve(window.turnstileParams);
            }
          }, 100);
        });
      });

      info('获取到Turnstile参数:', JSON.stringify(turnstileData));
    } catch (err) {
      throw err;
    }

    // 使用2captcha解决Turnstile挑战
    const token = await getTwoCaptchaTurnstileToken(turnstileData);

    // 将token传递给回调函数
    await page.evaluate((token) => {
      // 尝试多种方式设置token
      let success = false;

      console.log('尝试设置Turnstile响应:', token);

      // 方式1: 查找cf-turnstile-response输入框
      const responseInput = document.querySelector('[name="cf-turnstile-response"]');
      if (responseInput) {
        responseInput.value = token;
        success = true;
      }
      else {
        console.log('找不到cf-turnstile-response输入框');
      }

      // 方式2: 查找其他可能的Turnstile响应元素
      const turnstileResponse = document.querySelector('input[name*="turnstile"]') ||
        document.querySelector('textarea[name*="turnstile"]') ||
        document.querySelector('[id*="turnstile-response"]');
      if (turnstileResponse) {
        turnstileResponse.value = token;
        success = true;
      }
      else {
        console.log('找不到其他可能的Turnstile响应元素');
      }

      // 方式3: 直接调用回调函数（最重要的方式）
      if (window.tsCallback) {
        try {
          window.tsCallback(token);
          success = true;
        } catch (e) {
          console.error('调用tsCallback失败:', e);
        }
      }
      else {
        console.log('找不到全局的window.tsCallback函数');
      }

      // 方式4: 如果有全局的turnstile对象，尝试直接设置
      if (window.turnstile && window.turnstile.setResponse) {
        try {
          window.turnstile.setResponse(token);
          success = true;
        } catch (e) {
          console.error('调用turnstile.setResponse失败:', e);
        }
      }
      else {
        console.log('找不到全局的window.turnstile.setResponse');
      }

      return success;
    }, token);



    // 将令牌作为 cookie 注入
    await page.setCookie({
      name: "cf_clearance",
      value: token,
      "domain": ".lmarena.ai",
      "path": "/",
      "expires": 1767884072.695259,
      "httpOnly": true,
      "secure": true,
      "sameSite": "None"
    });


    info('Cloudflare Turnstile挑战已成功解决');

    // 等待页面可能的重定向或更新
    await page.waitForTimeout(2000);
    await smartNavigate(page, config.app.url, {
      timeout: config.app.pageTimeout
    });

    // 移除页面日志监听器
    page.off('console', consoleHandler);
    page.off('pageerror', errorHandler);
    page.off('response', responseHandler);
    info('已移除页面日志监听器');

    return true;

  } catch (err) {
    error('处理页面初始化阻挡元素时出现错误:', err.message);

    // 确保在错误情况下也移除监听器
    try {
      page.off('console', consoleHandler);
      page.off('pageerror', errorHandler);
      page.off('response', responseHandler);
      info('已移除页面日志监听器（错误处理）');
    } catch (removeErr) {
      error('移除监听器时出错:', removeErr.message);
    }

    return false;
  }
}

export async function initScript(page) {

}


/**
 * 使用二次贝塞尔曲线模拟人类的鼠标移动，并控制速度
 * @param {import('playwright').Page} page - Playwright 页面对象
 * @param {{x: number, y: number}} startPoint - 鼠标移动的起始点
 * @param {{x: number, y: number}} endPoint - 鼠标移动的终点
 * @param {object} [options] - 可选参数
 * @param {number} [options.steps=50] - 移动的步数。步数越多，轨迹越平滑，移动时间越长（速度越慢）。
 * @param {number} [options.deviation=150] - 曲线的弯曲程度。数值越大，弧度越大。
 * @param {number} [options.baseDelay=10] - 每一步移动之间的基础延迟（毫秒）。值越大，速度越慢。
 */
async function moveMouseWithBezier(page, startPoint, endPoint, options = {}) {
  const { steps = 50, deviation = 150, baseDelay = 10 } = options;

  // 为了制造弧线，我们需要一个在起点和终点连线之外的“控制点”
  const controlPoint = {
    x: (startPoint.x + endPoint.x) / 2 + (Math.random() - 0.5) * 2 * deviation,
    y: (startPoint.y + endPoint.y) / 2 + (Math.random() - 0.5) * 2 * deviation,
  };

  // 沿着贝塞尔曲线的路径，一步一步移动鼠标
  for (let i = 0; i <= steps; i++) {
    const t = i / steps;
    // 二次贝塞尔曲线公式
    const x = Math.pow(1 - t, 2) * startPoint.x + 2 * (1 - t) * t * controlPoint.x + Math.pow(t, 2) * endPoint.x;
    const y = Math.pow(1 - t, 2) * startPoint.y + 2 * (1 - t) * t * controlPoint.y + Math.pow(t, 2) * endPoint.y;

    await page.mouse.move(x, y);
    // 在每一步之间加入一个微小且随机的延迟，来控制整体速度
    await page.waitForTimeout(baseDelay + Math.random() * 10);
  }
}
/**
 * 处理页面初始化时的阻挡元素（主要是Cloudflare验证）
 * @param {Page} page - Playwright页面对象
 * @param {number} timeout - 等待验证元素出现的超时时间（毫秒），默认5000ms
 * @param {number} maxRetries - 最大重试次数，默认3次
 * @returns {Promise<boolean>} - 返回是否成功处理了阻挡元素
 */
// 把上面的 moveMouseWithBezier 函数放在这里

export async function handlePageInitialBlocks(page, timeout = 5000, maxRetries = 10) {
  return;
  let retryCount = 0;
  await page.waitForTimeout(5000)
  while (retryCount < maxRetries) {
    try {
      console.log(`🔄 开始第 ${retryCount + 1} 次尝试处理阻挡元素...`);

      // 您的定位代码，原封不动
      const hostLocator = page.locator('#ovEdv1 > div > div');
      console.log('✅ [层级 1] 已定位到主页面的 Shadow DOM 宿主。');
      await hostLocator.waitFor({ state: 'visible', timeout: 15000 });

      // 您的获取边界框代码，原封不动
      console.log('正在获取 iframe 的边界框 (Bounding Box)...');
      const iframeBox = await hostLocator.boundingBox();

      if (!iframeBox) {
        throw new Error('无法获取 iframe 的边界框，它可能不可见或不存在。');
      }
      console.log(`👍 iframe 边界框获取成功: x=${iframeBox.x}, y=${iframeBox.y}, width=${iframeBox.width}, height=${iframeBox.height}`);

      // =================== 核心修改在这里 ===================

      // 1. 定义移动的起点（屏幕边缘）和终点（您的目标坐标）
      const viewport = page.viewportSize() || { width: 1920, height: 1080 };
      const startPoint = { x: viewport.width - Math.random() * 10, y: Math.random() * viewport.height };
      const endPoint = { x: iframeBox.x + 35, y: iframeBox.y + 35 };

      console.log(`🚀 准备从边缘 (${startPoint.x.toFixed(2)}, ${startPoint.y.toFixed(2)}) 沿贝塞尔曲线缓慢移动...`);
      console.log(`🎯 目标点: (${endPoint.x.toFixed(2)}, ${endPoint.y.toFixed(2)})`);

      // 2. 【替换】调用我们新的贝塞尔曲线移动函数，替换掉您原来的 page.mouse.move
      await moveMouseWithBezier(page, startPoint, endPoint, {
        steps: 60,       // 步数调高，让移动更慢更平滑
        baseDelay: 12,   // 基础延迟调高，让速度进一步减慢
        deviation: 150   // 保持一个自然的弧度
      });

      // =================== 修改结束 ===================


      // 您的点击代码，原封不动
      console.log('准备模拟人类点击操作...');
      await page.waitForTimeout(Math.random() * 400 + 100);
      await page.mouse.down();
      await page.waitForTimeout(Math.random() * 100 + 50);
      await page.mouse.up();

      console.log('👍 点击完成！');

      // 您的后续所有检查代码，原封不动
      await page.waitForTimeout(5000);
      const elementStillExists = await page.locator('#ovEdv1').count() > 0;

      if (!elementStillExists) {
        console.log('✅ 阻挡元素已成功移除，处理完成！');
        return true;
      } else {
        console.log(`⚠️ 第 ${retryCount + 1} 次点击后，阻挡元素仍然存在，准备重试...`);
        retryCount++;
        if (retryCount < maxRetries) {
          await page.waitForTimeout(2000);
        }
      }

    } catch (error) {
      // 您的错误处理，原封不动
      console.error(`❌ 第 ${retryCount + 1} 次操作失败:`, error.message);
      retryCount++;
      if (retryCount < maxRetries) {
        console.log(`🔄 准备进行第 ${retryCount + 1} 次重试...`);
        await page.waitForTimeout(timeout);
      }
    }
  }

  console.error(`❌ 经过 ${maxRetries} 次尝试后仍无法处理阻挡元素`);
  return false;
}




let isCleaningUp = false;

/**
 * 清理浏览器资源。
 * 返回一个 Promise，在所有资源都关闭后解析。
 */
export async function cleanup() {
  if (!browser) {
    info('浏览器实例不存在，无需清理。');
    return;
  }
  info('🧹 开始清理浏览器资源...');

  // 清理浏览器环境管理器（包含所有用户的 context 和页面）
  if (browserEnvManager) {
    await browserEnvManager.cleanup();
    browserEnvManager = null;
    info('✅ 浏览器环境管理器已清理。');
  }

  if (browser) {
    await browser.close();
    browser = null;
    info('✅ 浏览器实例已关闭。');
  }
  info('✨ 所有资源均已成功释放。');
}

/**
 * 优雅停机处理函数。
 * @param {string} signal - 接收到的信号名称。
 */
async function gracefulShutdown(signal) {
  // 使用状态锁防止信号被多次触发时重复执行清理
  if (isCleaningUp) {
    info('清理已在进行中，请稍候...');
    return;
  }
  isCleaningUp = true;

  info(`\n🚨 收到信号 ${signal}。开始优雅停机...`);

  try {
    await cleanup();
    // 成功清理后，以代码 0 退出
    process.exit(0);
  } catch (err) {
    error('❌ 在优雅停机过程中发生错误:', err);
    // 清理失败后，以代码 1 退出，表示错误
    process.exit(1);
  }
}

/**
 * 设置进程退出处理。
 * 监听各种可能导致进程终止的信号。
 */
export function setupProcessHandlers() {
  // nodemon 使用 SIGUSR2 信号来重启
  process.once('SIGUSR2', () => gracefulShutdown('SIGUSR2'));

  // 监听 Ctrl+C (终端中断)
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  // 监听 `kill` 命令和 `node --watch` 的重启信号
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
}
