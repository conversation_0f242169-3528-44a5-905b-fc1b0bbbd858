#!/bin/bash

set -e

echo "🚀 开始设置容器开发环境..."

# 检查是否在容器内
if [ ! -f /.dockerenv ] && [ ! -f /proc/1/cgroup ] || ! grep -q docker /proc/1/cgroup 2>/dev/null; then
    echo "⚠️  警告: 似乎不在 Docker 容器内运行"
    echo "   本脚本专为容器内开发环境设计"
    read -p "   是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 1. 安装系统依赖
echo "📦 安装系统依赖..."
sudo apt-get update
sudo apt-get install -y \
    wget \
    curl \
    unzip \
    xvfb \
    x11vnc \
    fluxbox \
    xterm \
    x11-utils \
    procps \
    git \
    python3 \
    python3-pip

# 2. 安装 websockify
echo "🌐 安装 websockify..."
sudo pip3 install websockify

# 3. 安装 noVNC
echo "🖥️ 安装 noVNC..."
if [ ! -d "/opt/noVNC" ]; then
    sudo mkdir -p /opt
    cd /opt
    sudo git clone https://github.com/novnc/noVNC.git
    cd noVNC
    sudo ln -s vnc.html index.html
    cd - > /dev/null
fi

# 4. 安装项目依赖
echo "📚 安装项目依赖..."
npm install

# 5. 安装浏览器
echo "🌐 安装浏览器..."
npx playwright install chromium --with-deps
npx patchright install chromium --with-deps

# 6. 创建启动脚本
echo "📝 创建启动脚本..."
chmod +x dev-container-start.sh

# 7. 创建重启脚本
echo "🔄 创建重启脚本..."
chmod +x dev-container-restart.sh

# 8. 创建环境配置文件（如果不存在）
if [ ! -f ".env" ] && [ -f ".env.example" ]; then
    echo "⚙️ 创建环境配置文件..."
    cp .env.example .env
    echo "请编辑 .env 文件配置您的环境变量"
fi

echo "✅ 容器开发环境设置完成！"
echo ""
echo "🎯 下一步："
echo "1. 运行: ./dev-container-start.sh"
echo "2. 在 VSCode 中转发端口 6080 和 7860"
echo "3. 访问: http://localhost:6080 (noVNC)"
echo "4. 访问: http://localhost:7860 (应用)"
echo ""
echo "📚 更多信息请参考: CONTAINER_DEVELOPMENT.md"
