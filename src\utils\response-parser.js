import { info, warn, error } from './logger.js';

/**
 * 检查并返回错误。
 * @param {string} responseText - 响应文本。
 * @returns {{error: true, content: string}|null}
 */
function getResponseError(responseText) {
  if (responseText.startsWith('<!DOCTYPE html>')) {
    return {
      type: 'error',
      content: "被cf盾了"
    };
  }
  if (responseText.includes('{"error":{"code":')) {
    return {
      type: 'error',
      content: JSON.parse(responseText).error.message
    };
  }

  return null;
}



// 处理多个json可能连在一起的问题

/**
 * 处理JSON字符串，支持多个JSON对象连在一起的情况
 * @param {string} jsonStr - 输入的JSON字符串
 * @param {Function} callback - 处理完整JSON对象的回调函数
 * @returns {string} 剩余未处理的字符串
 */
function processJsonString(jsonStr, callback) {
  // 处理输入字符串
  let str = jsonStr.trim();
  let remainingBuffer = ""; // 存储未完成的片段

  let startPos = 0;
  let bracketCount = 0;
  let inQuotes = false;
  let escapeNext = false;
  let unicodeEscapeCount = 0; // 用于跟踪Unicode转义序列

  for (let i = 0; i < str.length; i++) {
    const char = str[i];

    // 处理Unicode转义序列 (\uXXXX)
    if (unicodeEscapeCount > 0) {
      unicodeEscapeCount--;
      continue;
    }

    // 处理普通转义字符
    if (escapeNext) {
      escapeNext = false;
      // 检查是否是Unicode转义序列的开始 (\u)
      if (char === 'u' && inQuotes) {
        unicodeEscapeCount = 4; // Unicode转义序列后面跟着4个十六进制字符
      }
      continue;
    }

    // 处理引号和转义
    if (char === '"' && !escapeNext) {
      inQuotes = !inQuotes;
    } else if (char === '\\' && inQuotes) {
      escapeNext = true;
    }

    // 只在不在引号内时计数括号
    if (!inQuotes) {
      if (char === '{') {
        if (bracketCount === 0) {
          startPos = i;
        }
        bracketCount++;
      } else if (char === '}') {
        bracketCount--;

        // 当找到一个完整的JSON对象时
        if (bracketCount === 0) {
          const jsonObject = str.substring(startPos, i + 1);
          try {
            // 调用处理单个JSON的函数
            callback(jsonObject);
          } catch (e) {
            console.error(`JSON解析错误:`, e);
            // 避免输出可能非常长的JSON字符串
            console.error(`JSON长度: ${jsonObject.length}字符`);
          }
        }
      }
    }
  }

  // 如果结束时仍有未闭合的括号，将未完成的JSON片段保存
  if (bracketCount > 0 && startPos < str.length) {
    // 保存未完成的JSON片段，等待下一次数据到达
    remainingBuffer = str.substring(startPos);
    // 记录日志，但不尝试处理不完整的JSON
    if (remainingBuffer.length > 100) {
      console.log(`保存未完成的JSON片段，长度: ${remainingBuffer.length}字符`);
    }
  }

  return remainingBuffer;
}

/**
 * 解析 Grok 的流式响应，处理流式JSON数据
 *
 * @param {string} buffer - 当前累积的响应字符串缓冲区。
 * @returns {{parsedItems: Array<{type: 'thinking' | 'text' | 'done' | 'error', content: string}>, remainingBuffer: string}}
 *          返回一个包含已解析项目和剩余缓冲区的对象。
 */
export function parseResponse(buffer) {
  const errorResponse = getResponseError(buffer);
  if (errorResponse) {
    return { parsedItems: [errorResponse], remainingBuffer: '' };
  }
  const parsedItems = [];

  // 使用processJsonString处理可能包含多个JSON对象的buffer
  const remainingBuffer = processJsonString(buffer, (jsonStr) => {
    try {
      const jsonObj = JSON.parse(jsonStr);

      // 检查是否有result.response结构
      if (jsonObj.result && jsonObj.result.response) {
        const response = jsonObj.result.response;

        // 检查是否有modelResponse字段，表示完成
        if (response.modelResponse) {
          parsedItems.push({
            type: 'done',
            content: response.modelResponse.message || ''
          });
          return;
        }

        // 检查是否有token字段
        if (response.token !== undefined && response.token !== "") {

          const type = response.isThinking ? 'thinking' : 'text';
          parsedItems.push({
            type: type,
            content: response.token
          });
        }
      }
    } catch (e) {
      console.error('解析JSON失败:', e);
      console.error('当前缓冲区:', buffer);
      console.error('原始JSON字符串:', jsonStr);
      // 可以选择添加错误项到parsedItems
      // parsedItems.push({
      //   type: 'error',
      //   content: 'JSON解析失败'
      // });
    }
  });

  return {
    parsedItems,
    remainingBuffer
  };
}


// --- OpenAI 格式化工具函数 (保持不变) ---

/**
 * 创建 OpenAI 格式的流式响应块 (chunk)。
 * @param {string} content - 文本内容。
 * @param {'thinking' | 'text'} type - 内容的类型。
 * @param {string} [model='grok3'] - 模型名称。
 * @returns {object}
 */
export function createStreamResponse(content, type, model = 'grok3') {
  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion.chunk',
    created: Math.floor(Date.now() / 1000),
    model,
    choices: [{
      index: 0,
      delta: {
        content: content || '',
        // 自定义字段，用于前端区分思考过程
        type: type || 'text',
      },
      logprobs: null,
      finish_reason: null
    }]
  };
}

/**
 * 创建 OpenAI 格式的错误响应。
 * @param {string} message - 错误消息。
 * @param {string} [model='grok3'] - 模型名称。
 * @returns {object}
 */
export function createErrorResponse(message, model = 'grok3') {
  return {
    object: 'error',
    message,
    type: 'invalid_request_error',
    model,
  };
}

/**
 * 创建 OpenAI 格式的非流式（完整）响应。
 * @param {string} content - 完整的响应内容。
 * @param {string} [model='grok3'] - 模型名称。
 * @returns {object}
 */
export function createNonStreamResponse(content, model = 'grok3') {
  return {
    id: `chatcmpl-${Date.now()}`,
    object: 'chat.completion',
    created: Math.floor(Date.now() / 1000),
    model,
    choices: [{
      index: 0,
      message: {
        role: 'assistant',
        content
      },
      finish_reason: 'stop'
    }],
    usage: {
      prompt_tokens: 0, // 未实现
      completion_tokens: 0, // 未实现
      total_tokens: 0 // 未实现
    }
  };
}