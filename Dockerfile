# 使用基于 Debian 的 Node.js 镜像
FROM node:23-bullseye-slim

# 设置基础环境变量
ENV NODE_ENV=production
ENV PORT=7860
ENV DISPLAY=:99
ENV XVFB_WHD=1920x1080x24
ENV XSERV_SUPPRESS_WARNINGS=1

# 设置工作目录
WORKDIR /app

# 更换 Debian 镜像源（系统级配置，变化频率最低）
RUN sed -i 's/deb.debian.org/mirrors.cloud.tencent.com/g' /etc/apt/sources.list && \
    sed -i 's/security.debian.org/mirrors.cloud.tencent.com/g' /etc/apt/sources.list

# 安装系统依赖和 Python 包（合并到一个层，减少层数）
RUN apt-get update && apt-get install -y \
    wget \
    curl \
    unzip \
    xvfb \
    x11vnc \
    fluxbox \
    xterm \
    x11-utils \
    procps \
    git \
    python3 \
    python3-pip \
    && rm -rf /var/lib/apt/lists/*

# 下载 noVNC（独立层，因为可能需要更新版本）
RUN pip3 install websockify && \
    cd /opt && \
    git clone https://github.com/novnc/noVNC.git && \
    cd noVNC && \
    ln -s vnc.html index.html

# 创建用户和基础目录结构（在安装依赖之前）
RUN groupadd -g 1001 nodejs && \
    useradd -u 1001 -g nodejs -m -d /home/<USER>
    mkdir -p /app/screenshots /app/logs && \
    chown -R nodejs:nodejs /home/<USER>/app && \
    chmod -R 777 /app/screenshots /app/logs /home/<USER>

# 复制 package 文件并安装 Node.js 依赖（分离层，利用缓存）
COPY package*.json ./
RUN npm ci --only=production

# 安装 Playwright 系统依赖（需要 root 权限）
RUN set -x && npx --yes patchright install-deps chromium && \
    npx --yes playwright install-deps chromium

RUN set -x && npx --yes patchright install-deps firefox 

# 切换到非 root 用户
USER nodejs

# 安装用户级 Playwright 浏览器（在 nodejs 用户下安装）
RUN set -x && npx --yes patchright install chromium && \
    npx --yes playwright install chromium

# 创建 camoufox 缓存目录并获取二进制文件
RUN set -x && mkdir -p /home/<USER>/.cache/camoufox &&   chmod -R 777 /home/<USER>/.cache/camoufox  && \
    npx --yes camoufox-js fetch

# 设置 HOME 环境变量（在用户创建之后）
# 设置 HOME 环境变量,  camoufox-js是nodejs用户安装到了/home/<USER>/.cache目录,camoufox-js包使用的是 path.join(os.homedir(), '.cache', appName)
# 需要设置HOME环境变量,如果没有设置则会回退到镜像(node:23-bullseye-slim)中已存在的/home/<USER>
ENV HOME=/home/<USER>

# 复制启动脚本（相对稳定的文件）
COPY --chown=nodejs:nodejs start-with-xvfb*.sh /app/
RUN chmod +x /app/start-with-xvfb*.sh

# 复制应用文件（变化频率最高，放在最后）
COPY --chown=nodejs:nodejs dist/ ./dist/
COPY --chown=nodejs:nodejs public/ ./public/

# 暴露端口
EXPOSE 7860 6080 5900

# 启动应用
CMD ["/app/start-with-xvfb.sh"]