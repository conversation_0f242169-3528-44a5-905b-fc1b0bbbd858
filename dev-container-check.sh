#!/bin/bash

echo "🔍 检查容器开发环境设置..."

# 检查必要的文件
echo "📁 检查文件..."
files=(
    "setup-container-dev.sh"
    "CONTAINER_DEVELOPMENT.md"
    "package.json"
    ".env.example"
)

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file 存在"
    else
        echo "❌ $file 不存在"
    fi
done

# 检查系统依赖（如果在 Linux 环境中）
if command -v apt-get &> /dev/null; then
    echo ""
    echo "🔧 检查系统依赖..."
    
    deps=(
        "curl"
        "wget"
        "git"
        "python3"
        "pip3"
    )
    
    for dep in "${deps[@]}"; do
        if command -v "$dep" &> /dev/null; then
            echo "✅ $dep 已安装"
        else
            echo "❌ $dep 未安装"
        fi
    done
fi

# 检查 Node.js 环境
echo ""
echo "📦 检查 Node.js 环境..."
if command -v node &> /dev/null; then
    echo "✅ Node.js 版本: $(node --version)"
else
    echo "❌ Node.js 未安装"
fi

if command -v npm &> /dev/null; then
    echo "✅ npm 版本: $(npm --version)"
else
    echo "❌ npm 未安装"
fi

# 检查项目依赖
echo ""
echo "📚 检查项目依赖..."
if [ -d "node_modules" ]; then
    echo "✅ node_modules 存在"
else
    echo "❌ node_modules 不存在，请运行 npm install"
fi

echo ""
echo "🎯 设置建议："
echo "1. 如果在容器内，运行: ./setup-container-dev.sh"
echo "2. 如果使用 Docker，运行: docker-compose -f docker-compose.yml -f docker-compose.novnc.yml up -d"
echo "3. 查看文档: CONTAINER_DEVELOPMENT.md"
