import { smartNavigate, handlePageInitialBlocks, simulateHumanRandomClicks, simulateHumanBehavior, compatEvaluate } from './browser.js';
import config from '../config.js';
import { info, warn, error } from './logger.js'
/**
 * 封装了与  Grok 页面交互的所有操作，
 * 如输入文本、点击按钮、设置模型和温度等。
 */
export class AppInjector {
  /**
   * @param {import('playwright').Page} page Playwright 页面对象
   * @param {object} [options={}] 配置选项
   * @param {string} [options.model] 模型 ID。
   * @param {number} [options.temperature] 温度值。
   * @param {string} [options.systemPrompt] 系统指令。
   * @param {string} [options.historyPrompt] 整合的指令。
   */
  constructor(page, options = {}) {
    this.page = page;
    this.options = options;
    this.modelMapping = Object.fromEntries(
      Object.entries(config.models).map(([key, model]) => [key, model.displayName])
    );
  }

  /**
   * 查找页面上的主输入框。
   * 增强了选择器和等待逻辑，以提高在动态加载页面上的成功率。
   * @returns {Promise<import('playwright').Locator|null>} 输入框的定位器。
   */
  async findInputElement() {
    try {
      // 等待查询栏容器出现
      await this.page.waitForSelector('.query-bar', { timeout: 10000 });

      // 查找 textarea 输入框
      const inputSelectors = [
        'textarea[aria-label*="Grok"]',
        'textarea[dir="auto"]',
        'textarea'
      ];

      for (const selector of inputSelectors) {
        try {
          const element = this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 2000 })) {
            info(`找到输入框: ${selector}`);
            return element;
          }
        } catch (err) {
          // 继续尝试下一个选择器
        }
      }

      warn('未找到输入框元素');
      return null;
    } catch (err) {
      error('查找输入框时出错:', err);
      return null;
    }
  }
  /**
   * 查找页面上的发送/运行按钮。
   * @returns {Promise<import('playwright').Locator|null>} 按钮的定位器。
   */
  async findSendButton() {
    try {


      // 查找发送按钮的选择器
      const buttonSelectors = [
        'button[type="submit"]',
        'button[type="submit"][aria-label*="提交"]',
        'button[type="submit"][aria-label*="Submit"]',
      ];

      for (const selector of buttonSelectors) {
        try {
          const element = this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 2000 })) {
            // 检查按钮是否可用（非禁用状态）
            const isEnabled = await element.isEnabled();
            if (isEnabled) {
              info(`找到可用的发送按钮: ${selector}`);
              return element;
            } else {
              info(`发送按钮存在但被禁用: ${selector}`);
            }
          }
        } catch (err) {
          // 继续尝试下一个选择器
        }
      }

      warn('未找到可用的发送按钮');
      return null;
    } catch (err) {
      error('查找发送按钮时出错:', err);
      return null;
    }
  }

  /**
 * 使用剪贴板方式快速填入长文本（最快的方法）。
 * @param {string} message 要发送的消息。
 * @returns {Promise<boolean>} 是否填充成功。
 */
  async fillMessageWithClipboard(message) {
    try {
      // 使用剪贴板API设置文本
      await compatEvaluate(this.page, (text) => {
        navigator.clipboard.writeText(text);
      }, message);

      // 等待剪贴板设置完成
      await this.page.waitForTimeout(100);

      // 使用 Ctrl+V 粘贴
      await this.page.keyboard.press('Control+v');


      return true;
    } catch (err) {
      error('剪贴板填充失败:', err);
      return false;
    }
  }


  /**
   * 在输入框中填入消息。
   * @param {string} message 要发送的消息。
   * @param {"auto"|"clipboard"|"direct"} fillMethod 填充方法。。
   * @returns {Promise<boolean>} 是否填充成功。
   */
  async fillMessage(message, fillMethod = 'auto') {
    const inputElement = await this.findInputElement();
    try {
      if (config.enableHumanSimulation && inputElement) {
        await simulateHumanRandomClicks(this.page, { referenceElement: inputElement });
      }
      // 根据方法选择填充策略
      let selectedMethod = fillMethod;
      if (fillMethod === 'auto') {
        // 自动选择最佳方法
        if (message.length > 2000 || !inputElement) {
          selectedMethod = 'clipboard';  // 超长文本用剪贴板
        } else {
          selectedMethod = 'direct';     // 短文本直接填充
        }
      }

      info(`使用 ${selectedMethod} 方法填充消息（${message.length} 字符）...`);

      switch (selectedMethod) {
        case 'clipboard':
          // 先清空输入框
          if (inputElement) {
            await inputElement.clear();
            // 点击输入框确保焦点
            await inputElement.click();
          }
          const clipboardResult = await this.fillMessageWithClipboard(message);
          // 检查剪贴板填充是否成功
          if (inputElement) {
            const currentValue = await inputElement.inputValue();
            if (!currentValue || currentValue.trim() === '') {
              info('剪贴板填充失败，使用直接填充作为备用方案...');
              await inputElement.fill(message);
              info('消息填充完成（备用直接填充）。');
              return true;
            }
          }

          info('消息填充完成（剪贴板方式）。');
          return clipboardResult;

        case 'direct':
        default:
          await inputElement.clear();
          await inputElement.fill(message);
          info('消息填充完成（直接填充）。');
          return true;
      }
    } catch (err) {
      error('填充消息失败:', err);
      return false;
    }
  }

  /**
   * 点击发送按钮发送消息。
   * @returns {Promise<boolean>} 是否发送成功。
   */
  async sendMessage() {
    if (config.enableHumanSimulation) {
      //await simulateHumanBehavior(this.page, { includeScrolling: false, duration: 1500 });
      const inputElement = await this.findInputElement();
      if (inputElement) {
        await simulateHumanRandomClicks(this.page, { referenceElement: inputElement });
      }
    }
    const sendButton = await this.findSendButton();

    // 如果找不到发送按钮，尝试使用回车键发送
    if (!sendButton) {
      info('无法找到发送按钮，尝试使用回车键发送...');
      try {
        await this.page.keyboard.press('Enter');
        info('已使用回车键发送消息。');
        return true;
      } catch (err) {
        error('使用回车键发送失败:', err);
        throw new Error('无法找到可用的发送按钮，且回车键发送也失败。');
      }
    }

    try {
      await sendButton.click();
      info('消息已发送。');
      return true;
    } catch (err) {
      error('点击发送按钮失败:', err);
      throw new Error('点击发送按钮失败');
    }
  }
  /**
   * 设置 AI 模型。
   * @param {string} modelName 模型 ID (例如 'grok3')。
   * @returns {Promise<boolean>} 是否设置成功。
   */
  async setModel(modelName) {
    try {
      info(`开始设置模型: ${modelName}`);

      // 如果模型名包含 "think"，则点击 Think 按钮
      if (modelName.includes("think")) {
        info('检测到 think 模型，尝试点击 Think 按钮');
        await this.clickThinkButton();
      }

      info(`模型设置完成: ${modelName}`);
      return true;
    } catch (err) {
      error('设置模型失败:', err);
      return false;
    }
  }

  /**
   * 点击 Think 按钮
   * @returns {Promise<boolean>} 是否点击成功
   */
  async clickThinkButton() {
    try {


      // 查找 Think 按钮的选择器
      const thinkButtonSelectors = [
        '.query-bar button[aria-label="Think"]',
        '.query-bar button:has-text("Think")',
        'button[aria-label="Think"]',
        'button:has-text("Think")',
        '.query-bar .group\\/think-toggle',
        'button.group\\/think-toggle'
      ];

      for (const selector of thinkButtonSelectors) {
        try {
          const element = this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 2000 })) {
            await element.click();
            info(`成功点击 Think 按钮: ${selector}`);

            // 等待按钮状态更新
            await this.page.waitForTimeout(500);
            return true;
          }
        } catch (err) {
          // 继续尝试下一个选择器
        }
      }

      warn('未找到 Think 按钮');
      return false;
    } catch (err) {
      error('点击 Think 按钮时出错:', err);
      return false;
    }
  }

  /**
   * 点击切换到 Private 模式
   * @returns {Promise<boolean>} 是否点击成功
   */
  async switchToPrivateMode() {
    try {
      info('尝试切换到 Private 模式...');

      // 查找 Private 模式按钮的选择器
      const privateButtonSelectors = [
        'a[href="/chat#private"]',
        'a[aria-label="Switch to Private Chat"]',
        'a[aria-label="切换到私密聊天"]',
        'a:has-text("Private")',
        'a:has-text("私密模式")',
      ];

      for (const selector of privateButtonSelectors) {
        try {
          const element = this.page.locator(selector).first();
          if (await element.isVisible({ timeout: 2000 })) {
            await element.click();
            info(`成功点击 Private 模式按钮: ${selector}`);

            // 等待页面切换
            await this.page.waitForTimeout(1000);
            return true;
          }
        } catch (err) {
          // 继续尝试下一个选择器
        }
      }

      warn('未找到 Private 模式按钮');
      return false;
    } catch (err) {
      error('切换到 Private 模式时出错:', err);
      return false;
    }
  }

  /**
   * 从页面源代码中获取用户信息
   * @param {string} ssoToken - SSO token
   * @returns {Promise<void>}
   */
  async getUserIdFromPageSource(ssoToken) {
    try {
      // 打印 ssoToken（只显示开头和末尾10个字符）
      if (ssoToken) {
        const tokenLength = ssoToken.length;
        if (tokenLength > 20) {
          const maskedToken = `${ssoToken.substring(0, 10)}...${ssoToken.substring(tokenLength - 10)}`;
          info(`使用 SSO token: ${maskedToken}`);
        }
        else {
          info(`使用 SSO token: ${ssoToken.substring(0, 10)}...`);
        }
      } else {
        info('未提供 SSO token');
      }

      info('尝试从页面源代码获取用户信息...');

      // 获取页面内容
      const pageContent = await this.page.content();

      // 搜索包含 userId 和 email 的模式
      const userInfoPattern = /\\"userId\\":\\"([^"]+)\\",\\"email\\":\\"([^"]+)\\"/;
      const match = pageContent.match(userInfoPattern);

      if (match && match[1] && match[2]) {
        const userId = match[1];
        const email = match[2];
        info(`用户信息 - userId: ${userId}, email: ${email}`);
      } else {
        warn('未能从页面源代码中找到用户信息');
      }
    } catch (err) {
      error('获取用户信息时出错:', err);
    }
  }

  async loadPage() {
    try {

      await smartNavigate(this.page, config.app.url, { timeout: config.app.pageTimeout });

      await this.page.waitForSelector('body', { timeout: 30000 });

      // 处理页面初始化阻挡元素（主要是Cloudflare验证）
      await handlePageInitialBlocks(this.page);

      // 如果启用了人类行为模拟，在导航后进行随机点击
      if (config.enableHumanSimulation) {
        info('启用了人类行为模拟，等待几百毫秒后进行随机点击...');
        await this.page.waitForTimeout(Math.floor(Math.random() * 300) + 200); // 200-500ms 随机等待
        await simulateHumanRandomClicks(this.page);
      }
      return true;
    } catch (err) {
      error('页面加载超时或失败:', err);
      return false;
    }
  }



  /**
   * 完整处理流程：加载页面、设置参数、填写并发送消息。
   * @param {string} message 要发送的消息。
   * @param {object} [options={}] 请求选项。
   * @param {string} [options.model] 模型 ID。
   * @param {number} [options.temperature] 温度值。
   * @param {string} [options.systemPrompt] 系统指令。
   * @returns {Promise<boolean>} 整个流程是否成功。
   */
  async processMessage() {
    info('开始处理消息...');


    // 1. 切换到 Private 模式
    await this.switchToPrivateMode();

    // 2. 获取用户信息
    //await this.getUserIdFromPageSource(options.ssoToken);

    if (this.options.model) await this.setModel(this.options.model);
    if (!await this.fillMessage(this.options.historyPrompt, "auto")) throw new Error('消息填写失败。');
    await this.page.waitForTimeout(500);
    if (!await this.sendMessage()) throw new Error('消息发送失败。');
    info('消息发送成功，等待网络拦截获取响应。');
    return true;
  }
}