import { info, error } from './logger.js'

/**
 * 封装了对  Grok 页面网络请求的流式拦截逻辑。
 */
export class StreamInterceptor {
  /** @readonly */
  TARGET_URL_PATTERNS = ["conversations/new"];



  // END_OF_STREAM_SIGNAL 已被移除

  /**
   * @param {import('playwright').Page} page Playwright 页面对象
   * @param {function(string): void} onStreamChunk 流数据块回调
   * @param {function(): void} onStreamEnd 流结束回调
   */
  constructor(page, onStreamChunk, onStreamEnd) {
    this.page = page;
    this.onStreamChunk = onStreamChunk;
    this.onStreamEnd = onStreamEnd;
    this.uniqueId = `interceptor_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    this.chunkCallbackName = `__onStreamChunk_${this.uniqueId}`;
    this.endCallbackName = `__onStreamEnd_${this.uniqueId}`;
    this.isInjected = false;
    this.isActive = false;
  }

  /**
   * 将拦截器脚本和所有配置一次性注入到页面中。
   * @private
   */
  async _inject() {
    if (this.isInjected) return;

    info(`[Interceptor] 注入流式拦截脚本 (ID: ${this.uniqueId})...`);
    await this.page.exposeFunction(this.chunkCallbackName, this.onStreamChunk);
    await this.page.exposeFunction(this.endCallbackName, this.onStreamEnd);

    const injectionPayload = {
      TARGET_URL_PATTERNS: this.TARGET_URL_PATTERNS, 
      chunkCallbackName: this.chunkCallbackName,
      endCallbackName: this.endCallbackName,
    };

    const browserScript = this._getBrowserScript();
    await this.page.evaluate(browserScript, injectionPayload);

    this.isInjected = true;
    info(`[Interceptor] 脚本注入和回调连接已一次性完成 (ID: ${this.uniqueId})`);
  }

  // ... activate 和 deactivate 方法保持不变 ...
  async activate() {
    if (this.isActive) return;
    await this._inject();

    info(`[Interceptor] 激活拦截器 (ID: ${this.uniqueId})...`);
    await this.page.evaluate(() => {
      if (window.__streamInterceptor) {
        window.__streamInterceptor.activate();
      }
    });
    this.isActive = true;
    info(`[Interceptor] 拦截器已激活 (ID: ${this.uniqueId})`);
  }

  async deactivate() {
    if (!this.isActive) return;

    info(`[Interceptor] 停用拦截器 (ID: ${this.uniqueId})...`);
    try {
      await this.page.evaluate(() => {
        if (window.__streamInterceptor) {
          window.__streamInterceptor.deactivate();
        }
      });
      this.isActive = false;
      info(`[Interceptor] 拦截器已停用 (ID: ${this.uniqueId})`);
    } catch (err) {
      error(`[Interceptor] 停用时发生错误 (可能页面已关闭): ${err.message}`);
    }
  }

  /**
   * 返回要注入到浏览器页面的脚本函数。
   * 这是最直接的版本，采用纯事件驱动模型。
   * @returns {function}
   * @private
   */
  _getBrowserScript() {
    return (payload) => {
      if (window.__streamInterceptor) {
        if (typeof window.__streamInterceptor.deactivate === 'function') {
          window.__streamInterceptor.deactivate();
        }
      }

      const {
        TARGET_URL_PATTERNS,
        chunkCallbackName,
        endCallbackName,
      } = payload;

 
      const originalFetch = window.fetch;

      const onChunkCallback = window[chunkCallbackName];
      const onEndCallback = window[endCallbackName];
      console.log(`[Interceptor] 回调已在注入时直接获取: ${chunkCallbackName}, ${endCallbackName}`);

      let interceptorActive = false;

      window.__streamInterceptor = {
        activate: () => {
          if (interceptorActive) return;
          console.log('🎯 [Interceptor] 激活 Fetch 拦截器...');
          interceptorActive = true;

          // 拦截 fetch API
          window.fetch = async function(input, init = {}) {
            const url = typeof input === 'string' ? input : input.url;
            const urlString = url ? url.toString() : '';
            const isTargetRequest = TARGET_URL_PATTERNS.some(pattern => urlString.includes(pattern));
            if (isTargetRequest && interceptorActive) {
              console.log('🎯 [Interceptor] 拦截到目标 fetch 请求:', urlString);

              try {
                const response = await originalFetch.call(this, input, init);

                if (!response.body) {
                  console.warn('[Interceptor] 响应没有 body，无法处理流');
                  return response;
                }

                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let fullResponseText = '';
                let streamEnded = false;

                const finalizeStream = () => {
                  if (streamEnded) return;
                  streamEnded = true;
                  console.log('🏁 [Interceptor] Fetch 流已结束，触发最终回调...');
                  if (onEndCallback) {
                    onEndCallback();
                  }
                };

                // 创建一个新的 ReadableStream 来代理原始流
                const stream = new ReadableStream({
                  start(controller) {
                    function pump() {
                      return reader.read().then(({ done, value }) => {
                        if (done) {
                          console.log('🏁 [Interceptor] Fetch 流读取完成');
                          finalizeStream();
                          controller.close();
                          return;
                        }

                        const chunk = decoder.decode(value, { stream: true });
                        fullResponseText += chunk;

                        // 发送数据块回调
                        if (onChunkCallback) {
                          onChunkCallback(chunk);
                        }

                        controller.enqueue(value);
                        return pump();
                      });
                    }

                    return pump();
                  }
                });

                // 返回一个新的 Response 对象，使用我们的代理流
                return new Response(stream, {
                  status: response.status,
                  statusText: response.statusText,
                  headers: response.headers
                });

              } catch (error) {
                console.error('[Interceptor] Fetch 拦截出错:', error);
                return originalFetch.call(this, input, init);
              }
            }

            return originalFetch.call(this, input, init);
          };
        },

        deactivate: () => {
          if (!interceptorActive) return;
          console.log('🔄 [Interceptor] 停用 Fetch 拦截器...');
          window.fetch = originalFetch;
          interceptorActive = false;
        }
      };
    };
  }
}