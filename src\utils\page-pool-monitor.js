import { getPagePoolStatus } from './browser.js';
import { info,warn,error } from './logger.js'

let monitorIntervalId = null;

/**
 * 开始监控页面池状态，并定期打印到控制台。
 * @param {number} [intervalMs=10000] - 监控间隔（毫秒）。
 */
export function startPagePoolMonitoring(intervalMs = 10000) {
  if (monitorIntervalId) {
    info('页面池监控已在运行中。');
    return;
  }

  info(`开始监控页面池状态，间隔: ${intervalMs}ms`);
  monitorIntervalId = setInterval(() => {
    const allStatus = getPagePoolStatus();
    const timestamp = new Date().toLocaleTimeString();

    // 计算总体统计
    let totalPages = 0;
    let totalAvailable = 0;
    let totalBusy = 0;

    for (const [userKey, status] of Object.entries(allStatus)) {
      totalPages += status.total;
      totalAvailable += status.available;
      totalBusy += status.busy;
    }

    const utilization = totalPages > 0 ? ((totalBusy / totalPages) * 100).toFixed(1) : 0;
    const userCount = Object.keys(allStatus).length;

    info(`[${timestamp}] 多用户页面池状态: 用户数=${userCount}, 总计=${totalPages}, 可用=${totalAvailable}, 忙碌=${totalBusy}, 使用率=${utilization}%`);
  }, intervalMs);
}

/**
 * 停止页面池监控。
 */
export function stopPagePoolMonitoring() {
  if (!monitorIntervalId) {
    info('页面池监控未在运行。');
    return;
  }

  info('停止页面池监控。');
  clearInterval(monitorIntervalId);
  monitorIntervalId = null;
}

/**
 * 打印一次详细的当前页面池状态。
 */
export function printPagePoolStatus() {
  const allStatus = getPagePoolStatus();
  const timestamp = new Date().toLocaleString();

  // 计算总体统计
  let totalPages = 0;
  let totalAvailable = 0;
  let totalBusy = 0;
  let maxSize = 0;

  for (const status of Object.values(allStatus)) {
    totalPages += status.total;
    totalAvailable += status.available;
    totalBusy += status.busy;
    maxSize = status.maxSize; // 假设所有用户池的最大大小相同
  }

  const utilization = totalPages > 0 ? ((totalBusy / totalPages) * 100).toFixed(1) : 0;
  const userCount = Object.keys(allStatus).length;

  info(`
--- 多用户页面池状态 @ ${timestamp} ---
  用户数:     ${userCount}
  总页面数:   ${totalPages}
  可用页面数: ${totalAvailable}
  忙碌页面数: ${totalBusy}
  每用户最大: ${maxSize}
  使用率:     ${utilization}%
------------------------------------`);

  // 打印每个用户的详细状态
  if (userCount > 0) {
    info('各用户详细状态:');
    for (const [userKey, status] of Object.entries(allStatus)) {
      const userUtilization = status.total > 0 ? ((status.busy / status.total) * 100).toFixed(1) : 0;
      const displayKey = userKey === 'default' ? 'default' : `${userKey.substring(0, 10)}...`;
      info(`  ${displayKey}: 总计=${status.total}, 可用=${status.available}, 忙碌=${status.busy}, 使用率=${userUtilization}%`);
    }
    info('------------------------------------');
  }
}