# Grok 2 API

一个基于 H3 v2beta 和 Playwright 的代理服务器，将  Grok 转换为 OpenAI 兼容的 API。

## 功能特性

- 🚀 基于 H3 v2beta 构建的高性能服务器

- 🎭 使用 Playwright 自动化浏览器操作

- 📡 支持流式和非流式响应

- 🔄 OpenAI API 兼容格式

- 🛡️ 错误处理和资源清理

- 🔧 模块化架构设计

- 🌍 环境变量配置支持

## 安装

```bash
# 克隆项目
git clone <repository-url>
cd grok2api

# 安装依赖
npm install

# 安装 Playwright 浏览器
npx playwright install chromium

# 复制环境变量配置文件
cp .env.example .env
```

## 使用方法

### 启动服务器

```bash
# 开发模式（带热重载）
npm run dev

# 生产模式
npm start
```

服务器将在 `http://localhost:3096` 启动。

### API 端点

#### 健康检查

```
GET /health
```

#### 模型列表

```
GET /v1/models
```

获取所有可用模型的列表，遵循 OpenAI API 规范。

**响应示例**：

```json
{
  "object": "list",
  "data": [
    {
      "id": "grok3",
      "object": "model",
      "created": **********,
      "owned_by": "google",
      "permission": [],
      "root": "grok3",
      "parent": null
    }
  ]
}
```

#### 单个模型信息

```
GET /v1/models/{model}
```

获取指定模型的详细信息。

**参数**：

- `model` (路径参数): 模型ID，如 `grok3`

**响应示例**：

```json
{
  "id": "grok3",
  "object": "model",
  "created": **********,
  "owned_by": "google",
  "permission": [],
  "root": "grok3",
  "parent": null
}
```

#### 聊天完成

```
POST /v1/chat/completions
```

**认证要求**: 需要在请求头中包含有效的 API Token

请求头：

```
Authorization: Bearer your_secret_api_token_here
Content-Type: application/json
```

请求格式（OpenAI 兼容）：

```json
{
  "model": "grok3",
  "messages": [
    {
      "role": "user",
      "content": "你好，请介绍一下你自己"
    }
  ],
  "stream": true
}
```

**cURL 示例**：

```bash
curl -X POST http://localhost:3095/v1/chat/completions \
  -H "Authorization: Bearer your_secret_api_token_here" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user",
        "content": "你好"
      }
    ],
    "stream": false
  }'
```

## 工作原理

1. **接收请求**: 服务器接收 OpenAI 格式的聊天请求

2. **启动浏览器**: 使用 Playwright 启动 Chromium 浏览器

3. **导航到 Grok**: 自动打开  Grok 网站

4. **注入脚本**: 在页面中注入 JavaScript 代码来填写和发送消息

5. **拦截响应**: 监听网络请求，拦截 Grok 的响应

6. **格式转换**: 将 grok 响应转换为 OpenAI 格式

7. **流式返回**: 通过 Server-Sent Events (SSE) 返回流式响应

## 配置

### 环境变量

复制 `.env.example` 到 `.env` 并根据需要修改配置：

```bash
# 服务器配置
PORT=3000
NODE_ENV=development

# 浏览器配置
HEADLESS=false
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# Grok 配置
GROK_URL=https://grok.com/
PAGE_TIMEOUT=30000
DEFAULT_MODEL=grok3

# CORS 配置
CORS_ORIGIN=*

# API 认证配置
API_TOKEN=your_secret_api_token_here

# Cookie 认证配置（二选一）
# 方式一：直接设置关键 Cookie 值（推荐）
SSO=your_secure_sso_value_here


# 方式二：完整 Cookie JSON 字符串
# COOKIES='[{"name":"sso","value":"...","domain":".grok.com",...}]'
```

### API 认证配置

为了保护 API 端点，支持两种认证模式：

#### 模式一：API Token 认证（默认）

1. **设置强密码**: 在 `.env` 文件中设置一个强密码作为 API Token

2. **请求认证**: 所有对 `/v1/chat/completions` 的请求都需要在 `Authorization` 头中包含有效的 Bearer token

3. **安全建议**:

   - 使用至少 32 位的随机字符串作为 token

   - 定期更换 token

   - 不要在代码中硬编码 token

   - 不要将 `.env` 文件提交到版本控制系统

#### 模式二：SSO 认证（多用户支持）

如果需要支持多用户，可以启用 SSO 认证模式：

1. **启用 SSO 模式**: 在 `.env` 文件中设置 `USE_APP_TOKEN=true`

2. **多用户支持**: 在此模式下，每个请求的 `Authorization` 头中的 Bearer token 将被视为用户的 SSO token

3. **自动隔离**: 系统会为每个不同的 SSO token 创建独立的浏览器上下文，实现用户间的完全隔离

4. **使用方式**:
   ```bash
   # 在 .env 文件中启用 SSO 模式
   USE_APP_TOKEN=true

   # 客户端请求时，将用户的 SSO token 作为 Bearer token
   curl -X POST http://localhost:3096/v1/chat/completions \
     -H "Authorization: Bearer user_sso_token_here" \
     -H "Content-Type: application/json" \
     -d '{"messages": [{"role": "user", "content": "Hello"}]}'
   ```

**生成安全 Token 示例**：

```bash
# 使用 openssl 生成随机 token
openssl rand -hex 32

# 或使用 Node.js
node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
```

## 获取 Cookies

为了访问  Grok，需要提供有效的认证 Cookies。有两种方式配置 Cookies：

### 方式一：使用环境变量（推荐）

直接设置 `SSO` 环境变量：

```bash
# 在 .env 文件中添加
SSO=your_secure_sso_value_here
```

**获取步骤**：

1. 在浏览器中登录 [ Grok](https://grok.com/)

2. 打开开发者工具 (F12)

3. 切换到 "Application" 或 "存储" 标签页

4. 在左侧找到 "Cookies" → "https://grok.com"

5. 找到以下两个 Cookie 并复制其值：

   - `sso`

6. 将这些值添加到 `.env` 文件中

### 方式二：使用完整 Cookie JSON

如果需要更复杂的 Cookie 配置，可以使用 `COOKIES` 环境变量：

```bash
# 在 .env 文件中添加
COOKIES='[{
    "name": "sso",
    "value": "",
    "domain": ".grok.com",
    "path": "/",
    "expires": -1,
    "httpOnly": true,
    "secure": true,
    "sameSite": "Lax"
  }]'
```

**完整 Cookie 格式**：

```json
[
  {
    "name": "__Secure-3PSID",
    "value": "your_secure_3psid_value_here",
    "domain": ".google.com",
    "path": "/",
    "expires": -1,
    "httpOnly": true,
    "secure": true,
    "sameSite": "None"
  },
  {
    "name": "__Secure-3PAPISID",
    "value": "your_secure_3papisid_value_here",
    "domain": ".google.com",
    "path": "/",
    "expires": -1,
    "httpOnly": false,
    "secure": true,
    "sameSite": "None"
  }
]
```

### 配置优先级

系统会按以下优先级使用 Cookie 配置：

1. **COOKIES** 环境变量（如果存在）

2. **SECURE_3PSID** + **SECURE_3PAPISID** 环境变量（如果 COOKIES 不存在）

3. **cookies.json** 文件（如果环境变量都不存在）

### 安全注意事项

- 🔒 **保护 Cookie 值**：这些 Cookie 包含您的  账户认证信息，请妥善保管

- 🚫 **不要分享**：不要将包含真实 Cookie 值的 `.env` 文件提交到版本控制系统

- ⏰ **定期更新**： 可能会定期更新这些 Cookie，如果遇到认证失败，请重新获取

- 🔄 **会话管理**：如果在其他地方登出  账户，这些 Cookie 可能会失效

## 许可证

MIT License