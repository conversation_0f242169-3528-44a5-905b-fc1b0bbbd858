{"name": "grok2api", "version": "1.0.0", "description": " Grok to OpenAI API proxy using H3 and Playwright", "main": "dist/index.js", "type": "module", "scripts": {"login": "node src/login.js", "dev": "node src/web-server.js", "build": "node build.js", "start": "node dist/index.js"}, "keywords": ["h3", "playwright", "openai", "proxy"], "author": "", "license": "MIT", "dependencies": {"camoufox-js": "^0.6.0", "dotenv": "^17.0.1", "fingerprint-generator": "^2.1.69", "fingerprint-injector": "^2.1.69", "h3": "^2.0.0-beta.1", "patchright": "^1.52.5", "playwright": "^1.53.2", "playwright-extra": "^4.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^20.10.0", "esbuild": "^0.25.5"}}