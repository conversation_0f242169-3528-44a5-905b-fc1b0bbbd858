#!/bin/bash

# 设置日志文件
LOG_FILE="/tmp/cloudflared-tunnel.log"
TOKEN=""
 
echo "=========================================="
echo "开始执行 cloudflared tunnel 命令"
echo "时间: $(date)"
echo "=========================================="

# 检查 cloudflared 是否已安装
if ! command -v cloudflared &> /dev/null; then
    echo "错误: cloudflared 未安装，尝试安装..."
    # 这里可以添加安装 cloudflared 的命令
    # 例如: curl -L --output cloudflared.deb https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb
    # dpkg -i cloudflared.deb
fi

echo "执行 cloudflared tunnel run 命令..."
echo "命令将在后台运行，日志输出到 $LOG_FILE"

service cloudflared start

# 在后台运行 cloudflared tunnel 命令并将输出重定向到日志文件
# 注意: 请将下面的 token 替换为您的完整 token
cloudflared tunnel run --token $TOKEN > $LOG_FILE 2>&1 &

# 保存进程 ID
TUNNEL_PID=$!
echo "cloudflared tunnel 进程 ID: $TUNNEL_PID"
echo "可以使用 'tail -f $LOG_FILE' 查看实时日志"

# 等待几秒检查进程是否仍在运行
sleep 5
if ps -p $TUNNEL_PID > /dev/null; then
    echo "✅ cloudflared tunnel 已成功启动并在后台运行"
else
    echo "❌ cloudflared tunnel 启动失败，请检查日志: $LOG_FILE"
    cat $LOG_FILE
fi

echo "=========================================="
echo "dev-container-local.sh 执行完成"
echo "时间: $(date)"
echo "=========================================="
