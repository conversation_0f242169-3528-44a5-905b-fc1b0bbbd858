import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// 加载环境变量文件
// 先加载 .env 文件（默认配置）
dotenv.config({ path: '.env' });
// 再加载 .env.local 文件（本地覆盖配置，优先级更高）
dotenv.config({ path: '.env.local' });

/**
 * @typedef {Object} AppConfig
 * @property {Object} server - 服务器配置
 * @property {number} server.port - 监听端口
 * @property {string} server.host - 监听主机
 * @property {Object} browser - Playwright 浏览器配置
 * @property {chromium | firefox | webkit | camoufox | patchright } browser.type - 浏览器类型：chromium、camoufox 或 patchright
 * @property {boolean} browser.headless - 是否以无头模式运行
 * @property {number} browser.timeout - 浏览器操作的全局超时时间
 * @property {string|undefined} browser.executablePath - Chromium 可执行文件路径
 * @property {Function} browser.args - 浏览器启动参数函数，根据当前配置的浏览器类型返回参数数组
 * @property {string} cookieFile - Cookie 存储文件路径
 * @property {string|undefined} cookiesFromEnv - 从环境变量读取的 Cookie 字符串或构造的 Cookie JSON
 * @property {string} screenshotDir - 截图保存目录
 * @property {Object} app -  App 相关配置
 * @property {string} app.url - App 的目标 URL
 * @property {number} app.responseTimeout - 等待 AI 响应的超时时间
 * @property {number} app.pageTimeout - 页面加载的超时时间
 * @property {string} app.defaultModel - 默认使用的模型 ID
 * @property {number} app.maxTokens - 支持的最大令牌数 (此为示例，实际限制在网页端)
 * @property {number} app.temperature - 默认温度
 * @property {string|undefined} app.token - API 访问令牌
 * @property {boolean} app.useAppToken - 是否使用 APP_TOKEN 认证模式
 * @property {Object.<string, Object>} models - 支持的模型列表
 * @property {boolean} enableHumanSimulation - 是否启用人类行为模拟
 * @property {boolean} disablePagePool - 是否禁用页面池（禁用后每次使用完页面直接关闭）
 * @property {boolean} alwaysNewContext - 是否每次都创建新的浏览器上下文（禁用上下文复用）
 * @property {string[]} targetUrlPatterns - 流式拦截器要监听的URL模式列表
 * @property {Function} createCookiesByToken - 根据APP_TOKEN token创建cookie对象数组的函数
 * @property {string|undefined} twoCaptchaKey - 2captcha API密钥
 */


const browserType = process.env.BROWSER_TYPE || 'chromium';
 


const createCookiesByToken = (token) => {
  return [
    {
      "name": "arena-auth-prod-v1",
      "value": token,
      "domain": "lmarena.ai",
      "path": "/",
      "expires": -1,
      "httpOnly": false,
      "secure": false,
      "sameSite": "Lax"
    }
  ];
};

/** @type {AppConfig} */
const config = {
  // 服务器配置
  server: {
    port: parseInt(process.env.PORT || '7860', 10),
    host: process.env.HOST || '0.0.0.0'
  },

  // Playwright 浏览器配置
  browser: {
    // 浏览器类型：chromium 或 camoufox
    type: browserType,
    headless: (process.env.HEADLESS || 'true').toLowerCase() !== 'false',
    timeout: parseInt(process.env.TIMEOUT || '60000', 10), // 增加到60秒
    // 根据浏览器类型选择可执行文件路径
    executablePath: (() => {
      if (browserType === 'camoufox') {
        // camoufox.js会使用os.homedir()获取用户主目录
        return undefined;
      }
      return process.env.PLAYWRIGHT_EXECUTABLE_PATH;
    })(),

    args: (() => {
      const baseArgs = [
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-infobars',
        '--disable-extensions',
      ];

      // 如果是 chromium，添加防检测参数
      if (browserType === 'chromium') {
        return [
          '--disable-blink-features=AutomationControlled',
          ...baseArgs
        ];
      }
      // camoufox Docker环境优化参数
      if (browserType === 'camoufox') {
        return [
          ...baseArgs
        ];
      }
      // patchright 使用默认参数（Patchright 会自动处理防检测）
      if (browserType === 'patchright') {
        return baseArgs;
      }
      return baseArgs;
    })()
  },

  // Cookie 配置
  cookieFile: process.env.COOKIE_FILE || './cookies.json',
  cookiesFromEnv: (() => {
    // 如果环境变量 COOKIES 存在，直接使用
    if (process.env.COOKIES) {
      return process.env.COOKIES;
    }

    // 如果 COOKIES 不存在，但  OTHER_COOKIE_NAME 存在，则构造 cookie 数组
    if (process.env.TOKEN_COOKIE) {
      return JSON.stringify(createCookiesByToken(process.env.TOKEN_COOKIE));
    }
    // 如果都不存在，返回 undefined
    return undefined;
  })(),

  createCookiesByToken: createCookiesByToken,

  // 截图目录
  screenshotDir: './screenshots',

  // app 配置
  app: {
    url: process.env.WEB_URL || 'https://grok.com',
    responseTimeout: parseInt(process.env.RESPONSE_TIMEOUT || '600000', 10), // 10分钟
    pageTimeout: parseInt(process.env.PAGE_TIMEOUT || '30000', 10), // 30秒
    defaultModel: process.env.DEFAULT_MODEL || 'grok4',
    maxTokens: 65536, // 理论值，实际由网页决定
    temperature: 1,
    token: process.env.API_TOKEN,
    useAppToken: (process.env.USE_APP_TOKEN || 'false').toLowerCase() === 'true'
  },

  // 可用模型列表 (遵循 OpenAI API 格式) -
  models: {
    'grok3': {
      displayName: 'Grok3',
      id: 'grok3',
      object: 'model',
      created: 1704067200, // 2024-01-01
      permission: [],
      root: 'grok3',
      parent: null
    },
    'grok3-think': {
      displayName: 'Grok3-think',
      id: 'grok3-think',
      object: 'model',
      created: 1704067200, // 2024-01-01
      permission: [],
      root: 'grok3-think',
      parent: null
    },
  },

  // 人类行为模拟配置
  enableHumanSimulation: (process.env.ENABLE_HUMAN_SIMULATION || 'false').toLowerCase() === 'true',

  // 页面池配置
  disablePagePool: (process.env.DISABLE_PAGE_POOL || 'false').toLowerCase() === 'true',

  // 浏览器上下文配置
  alwaysNewContext: (process.env.ALWAYS_NEW_CONTEXT || 'false').toLowerCase() === 'true',

  // 流式拦截器配置
  targetUrlPatterns: process.env.TARGET_URL_PATTERNS
    ? process.env.TARGET_URL_PATTERNS.split(',').map(pattern => pattern.trim())
    : ['stream/create-evaluation'],

  // 2captcha API密钥
  twoCaptchaKey: process.env.TWO_CAPTCHA_KEY
};

export default config;