#!/bin/bash

# 清理可能存在的旧 X 服务器锁文件
echo "清理旧的 X 服务器锁文件..."
rm -f /tmp/.X99-lock 2>/dev/null || true
# 尝试清理 X11 socket，如果没有权限则忽略
rm -f /tmp/.X11-unix/X99 2>/dev/null || true

# 启动 Xvfb (虚拟显示服务器)
echo "启动 Xvfb 虚拟显示服务器..."
Xvfb :99 -screen 0 $XVFB_WHD -ac +extension GLX +render -noreset 2>/dev/null &
XVFB_PID=$!

# 等待 Xvfb 启动
sleep 3

# 验证 Xvfb 是否成功启动
if ! xdpyinfo -display :99 >/dev/null 2>&1; then
    echo "❌ Xvfb 启动失败，尝试重新启动..."
    sleep 2
    if ! xdpyinfo -display :99 >/dev/null 2>&1; then
        echo "❌ Xvfb 仍然无法启动，退出..."
        exit 1
    fi
fi
echo "✅ Xvfb 启动成功"

# 启动窗口管理器 (fluxbox)
echo "启动窗口管理器..."
fluxbox &
FLUXBOX_PID=$!

# 启动 VNC 服务器 (用于远程查看)
echo "启动 VNC 服务器..."
echo "VNC 服务器将在端口 5900 上运行，无密码访问"
# 浏览器 ←→ noVNC ←→ websockify ←→ x11vnc ←→ X11显示器
# 使用更稳定的 VNC 配置，强制指定分辨率并禁用可能导致问题的功能
x11vnc -display :99 -nopw -listen 0.0.0.0 -xkb \
    -geometry 1920x1080 \
    -forever -shared \
    -noxdamage -noxfixes \
    -nowireframe -noscrollcopyrect \
    -quiet &
VNC_PID=$!

# 等待 VNC 服务器启动
sleep 2

# 启动 websockify (WebSocket 到 VNC 的代理)
echo "启动 websockify..."
echo "websockify 将在端口 6080 上运行，连接到本地 VNC 服务器"
# 添加 --heartbeat 参数以保持连接活跃
websockify --web=/opt/noVNC --heartbeat=30 6080 localhost:5900 &
WEBSOCKIFY_PID=$!

# 等待 websockify 启动
sleep 2

# 设置信号处理函数，确保优雅关闭
cleanup() {
    echo "正在关闭服务..."
    
    # 关闭 Node.js 应用
    if [ ! -z "$NODE_PID" ]; then
        kill $NODE_PID 2>/dev/null
        wait $NODE_PID 2>/dev/null
    fi
    
    # 关闭 websockify
    if [ ! -z "$WEBSOCKIFY_PID" ]; then
        kill $WEBSOCKIFY_PID 2>/dev/null
    fi
    
    # 关闭 VNC 服务器
    if [ ! -z "$VNC_PID" ]; then
        kill $VNC_PID 2>/dev/null
    fi
    
    # 关闭窗口管理器
    if [ ! -z "$FLUXBOX_PID" ]; then
        kill $FLUXBOX_PID 2>/dev/null
    fi
    
    # 关闭 Xvfb
    if [ ! -z "$XVFB_PID" ]; then
        kill $XVFB_PID 2>/dev/null
    fi
    
    echo "所有服务已关闭"
    exit 0
}

# 注册信号处理
trap cleanup SIGTERM SIGINT

echo "🌐 noVNC Web 界面已启动！"
echo "📱 通过浏览器访问: http://localhost:6080"
echo "🖥️  直接 VNC 连接: localhost:5900"
echo "🚀 应用服务: http://localhost:7860"

# 启动 Node.js 应用
echo "启动 Node.js 应用..."
npm start &
NODE_PID=$!

# 等待 Node.js 应用结束
wait $NODE_PID

# 如果 Node.js 应用意外退出，执行清理
cleanup
