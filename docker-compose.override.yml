# Docker Compose Override 文件
# 这个文件会自动与 docker-compose.yml 合并，用于开发环境配置
# 运行 docker-compose up 时会自动应用这些覆盖配置

services:
  lmarena2api:

    # 开发环境的卷挂载配置
    volumes:
      # 挂载 cookies.json 文件
      - "./cookies.json:/app/cookies.json"
      # 可选：挂载日志目录到主机（便于查看日志）
      - "./logs:/app/logs"
      # 可选：挂载截图目录到主机（便于查看截图）
      - "./screenshots:/app/screenshots"
      
      # --- 开发模式的核心 ---
      # 将本地的构建产物目录挂载到容器内
      - "./dist:/app/dist"
      # 将本地的公共资源目录挂载到容器内
      - "./public:/app/public"
      # (可选，但很常见) 甚至可以把源码也挂载进去，
      - "./src:/app/src"
      # 运行脚本
      - "./start-with-xvfb-dev.sh:/app/start-with-xvfb-dev.sh"
    
    # 开发环境的环境变量覆盖
    environment:
      # 开发模式
      - NODE_ENV=development
      # 开发时可能需要看到浏览器界面（使用 Xvfb 有头模式）
      - HEADLESS=false
      # 开发时更详细的日志
      - LOG_LEVEL=DEBUG
      # 开发时使用浏览器
      - BROWSER_TYPE=camoufox
      # Xvfb 配置
      - DISPLAY=:99
      - XVFB_WHD=1920x1080x24
    
    # 开发时可能需要的额外端口映射
    # ports:
    #   - "9229:9229"  # Node.js 调试端口
    #   - "5900:5900"  # VNC 端口 (如果启用了 x11vnc)
    
    # 开发时的启动命令覆盖（使用 Xvfb 开发模式启动脚本）
    command: ["/app/start-with-xvfb-dev.sh"]
    
    # 开发时禁用健康检查（可选，减少日志噪音）
    healthcheck:
      disable: true




# docker-compose.yml 会默认读取根目录的.env文件  所以要直接设置为HEADLESS=true
# 首次启动 : docker-compose up -d --build  (--build 会确保使用最新的 Dockerfile 构建镜像)
# 启动服务: docker-compose up -d
# 停止服务: docker-compose down
# 重启服务: docker-compose restart
# 查看日志: docker-compose logs -f
# 查看状态: docker-compose ps