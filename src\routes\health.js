import { getPagePoolStatus } from '../utils/browser.js';

/**
 * 处理 `/health` 的 GET 请求，提供服务健康状态。
 * @param {import('h3').H3Event} event - H3 事件对象。
 * @returns {object} 包含服务状态和页面池信息的健康报告。
 */
export function healthHandler(event) {
  const allPagePoolStatus = getPagePoolStatus();

  // 计算总体统计
  let totalPages = 0;
  let totalAvailable = 0;
  let totalBusy = 0;
  let maxSize = 0;

  for (const status of Object.values(allPagePoolStatus)) {
    totalPages += status.total;
    totalAvailable += status.available;
    totalBusy += status.busy;
    maxSize = status.maxSize; // 假设所有用户池的最大大小相同
  }

  const utilization = totalPages > 0
    ? Math.round((totalBusy / totalPages) * 100)
    : 0;

  const userCount = Object.keys(allPagePoolStatus).length;

  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    pagePool: {
      userCount,
      total: totalPages,
      available: totalAvailable,
      busy: totalBusy,
      maxSizePerUser: maxSize,
      utilization: `${utilization}%`,
      users: allPagePoolStatus
    }
  };
}