import { createError } from 'h3';
import config from '../config.js';

/**
 * 处理 `/v1/models` 的 GET 请求，返回所有可用模型的列表。
 * @param {import('h3').H3Event} event - H3 事件对象。
 * @returns {Promise<object>} OpenAI 格式的模型列表响应。
 */
export async function modelsHandler(event) {
  try {
    const models = Object.values(config.models).map(model => ({
      id: model.id,
      object: model.object,
      created: model.created,
      owned_by: model.owned_by,
      permission: model.permission,
      root: model.root,
      parent: model.parent
    }));

    // 按创建时间降序排序
    models.sort((a, b) => b.created - a.created);

    return {
      object: 'list',
      data: models
    };
  } catch (err) {
    console.error('获取模型列表时出错:', err);
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to retrieve models list.'
    });
  }
}

/**
 * 处理 `/v1/models/:model` 的 GET 请求，返回单个模型的详细信息。
 * @param {import('h3').H3Event} event - H3 事件对象。
 * @returns {Promise<object>} OpenAI 格式的单个模型信息。
 */
export async function modelHandler(event) {
  try {
    const modelId = event.context.params?.model;

    if (!modelId) {
      throw createError({ statusCode: 400, statusMessage: 'Model ID is required.' });
    }

    const model = config.models[modelId];

    if (!model) {
      throw createError({ statusCode: 404, statusMessage: `Model '${modelId}' not found.` });
    }

    return {
      id: model.id,
      object: model.object,
      created: model.created,
      owned_by: model.owned_by,
      permission: model.permission,
      root: model.root,
      parent: model.parent
    };
  } catch (err) {
    console.error(`获取模型 '${event.context.params?.model}' 信息时出错:`, err);
    if (err.statusCode) {
      throw err;
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to retrieve model information.'
    });
  }
}