#!/bin/bash

# 监控脚本 - 检查 dev-container 服务状态并自动重启
# 用法: 每分钟执行一次此脚本来监控服务状态

LOG_FILE="./logs/monitor.log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# 确保日志目录存在
mkdir -p ./logs

# 记录日志函数
log_message() {
    echo "[$TIMESTAMP] $1" | tee -a "$LOG_FILE"
}

# 检查进程是否存在的函数
check_process() {
    local process_name="$1"
    if pgrep -f "$process_name" > /dev/null; then
        return 0  # 进程存在
    else
        return 1  # 进程不存在
    fi
}

# 检查关键服务进程
check_services() {
    local services_down=0
    
    # 检查 Xvfb (虚拟显示服务器)
    if ! check_process "Xvfb :99"; then
        log_message "⚠️  Xvfb 服务未运行"
        services_down=1
    fi
    
    # 检查 x11vnc (VNC 服务器)
    if ! check_process "x11vnc.*:99"; then
        log_message "⚠️  x11vnc 服务未运行"
        services_down=1
    fi
    
    # 检查 websockify (WebSocket 代理)
    if ! check_process "websockify.*6080"; then
        log_message "⚠️  websockify 服务未运行"
        services_down=1
    fi
    
    # 检查 fluxbox (窗口管理器)
    if ! check_process "fluxbox"; then
        log_message "⚠️  fluxbox 窗口管理器未运行"
        services_down=1
    fi

    # 检查 窗口管理器 (窗口管理器)
    if ! check_process "cloudflared"; then
        log_message "⚠️  cloudflared 未运行"
        services_down=1
    fi
    
    # 检查 Node.js 应用
    if ! check_process "npm run dev"; then
        log_message "⚠️  Node.js 应用未运行"
        services_down=1
    fi
    
    return $services_down
}

# 检查端口是否监听
check_ports() {
    local ports_down=0
    
    # 检查 VNC 端口 5900
    if ! netstat -ln 2>/dev/null | grep -q ":5900.*LISTEN" && ! ss -ln 2>/dev/null | grep -q ":5900.*LISTEN"; then
        log_message "⚠️  VNC 端口 5900 未监听"
        ports_down=1
    fi
    
    # 检查 noVNC 端口 6080
    if ! netstat -ln 2>/dev/null | grep -q ":6080.*LISTEN" && ! ss -ln 2>/dev/null | grep -q ":6080.*LISTEN"; then
        log_message "⚠️  noVNC 端口 6080 未监听"
        ports_down=1
    fi
    
    # 检查应用端口 7860
    if ! netstat -ln 2>/dev/null | grep -q ":7860.*LISTEN" && ! ss -ln 2>/dev/null | grep -q ":7860.*LISTEN"; then
        log_message "⚠️  应用端口 7860 未监听"
        ports_down=1
    fi
    
    return $ports_down
}

# 重启服务函数
restart_services() {
    log_message "🔄 检测到服务异常，开始重启服务..."
    
    # 检查重启脚本是否存在
    if [ -f "./dev-container-restart.sh" ]; then
        log_message "📝 执行重启脚本: ./dev-container-restart.sh"
        ./dev-container-restart.sh >> "$LOG_FILE" 2>&1 &
        
        # 等待一段时间让服务启动
        sleep 10
        
        log_message "✅ 重启命令已执行，等待服务启动..."
    else
        log_message "❌ 重启脚本 ./dev-container-restart.sh 不存在"
        
        # 如果重启脚本不存在，尝试直接启动
        if [ -f "./dev-container-start.sh" ]; then
            log_message "📝 尝试执行启动脚本: ./dev-container-start.sh"
            ./dev-container-start.sh >> "$LOG_FILE" 2>&1 &
            sleep 10
            log_message "✅ 启动命令已执行，等待服务启动..."
        else
            log_message "❌ 启动脚本 ./dev-container-start.sh 也不存在，无法自动重启"
            return 1
        fi
    fi
    
    return 0
}

# 主监控逻辑
main() {
    log_message "🔍 开始监控 dev-container 服务状态..."
    
    # 检查服务进程
    if ! check_services; then
        log_message "❌ 发现服务进程异常"
        restart_services
        return
    fi
    
    # 检查端口监听
    if ! check_ports; then
        log_message "❌ 发现端口监听异常"
        restart_services
        return
    fi
    
    # 所有检查都通过
    log_message "✅ 所有服务运行正常"
}

# 执行主函数
main

# 清理旧日志文件（保留最近7天）
find ./logs -name "monitor.log*" -mtime +7 -delete 2>/dev/null || true
