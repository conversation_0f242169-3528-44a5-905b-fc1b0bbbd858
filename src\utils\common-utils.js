import fs from 'node:fs';
import path from 'node:path';
import readline from 'node:readline';
import { info, error } from './logger.js';

/**
 * 创建一个人类可读的时间戳字符串。
 * @returns {string} 格式为 'YYYY-MM-DD_HH-MM-SS' 的时间戳。
 */
export function getHumanReadableTimestamp() {
  return new Date().toISOString().replace(/T/, '_').replace(/:/g, '-').replace(/\..+/, '');
}

/**
 * 确保指定的目录存在，如果不存在则创建它。
 * @param {string} dir - 目录路径。
 */
export function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    info(`已创建目录: ${dir}`);
  }
}

/**
 * 检查 Cookie 是否可用（通过环境变量或文件）。
 * @param {string} cookieFile - Cookie 文件路径。
 * @param {string|undefined} cookiesFromEnv - 环境变量中的 Cookie 字符串。
 * @returns {boolean} Cookie 是否可用。
 */
export function checkCookieAvailability(cookieFile, cookiesFromEnv) {
  if (cookiesFromEnv) {
    try {
      JSON.parse(cookiesFromEnv);
      info('发现并验证了环境变量中的 COOKIES。');
      return true;
    } catch (err) {
      error('环境变量 COOKIES 格式无效 (必须是 JSON 数组字符串):', err.message);
    }
  }
  if (fs.existsSync(cookieFile)) {
    info(`发现 Cookie 文件: ${cookieFile}`);
    return true;
  }
  error(`Cookie 文件不存在: ${cookieFile}，且未设置 COOKIES 环境变量。`);
  info('请先运行 `npm run login` 或设置 COOKIES 环境变量。');
  return false;
}

/**
 * 加载 Cookie，优先从环境变量读取，其次从文件读取。
 * @param {string} cookieFile - Cookie 文件路径。
 * @param {string|undefined} cookiesFromEnv - 环境变量中的 Cookie 字符串。
 * @returns {object[]} Cookie 对象数组。
 */
export function loadCookies(cookieFile, cookiesFromEnv) {
  try {
    if (cookiesFromEnv) {
      info('从环境变量 COOKIES 加载 Cookie...');
      return JSON.parse(cookiesFromEnv);
    }
    if (fs.existsSync(cookieFile)) {
      info(`从文件加载 Cookie: ${cookieFile}`);
      const fileContent = fs.readFileSync(cookieFile, 'utf8').trim();
      if (fileContent) {
        return JSON.parse(fileContent);
      }
    }
  } catch (err) {
    error('加载 Cookie 失败:', err);
    throw new Error('无法加载或解析 Cookie。');
  }
  return [];
}

/**
 * 保存页面截图。
 * @param {import('playwright').Page} page - Playwright 页面对象。
 * @param {string} screenshotDir - 截图保存目录。
 * @param {string} [prefix='screenshot'] - 截图文件名前缀。
 * @param {boolean} [usetimestamp=true] - 是否在文件名中包含时间戳。
 * @returns {Promise<string>} 截图文件的完整路径。
 */
export async function saveScreenshot(page, screenshotDir, prefix = 'screenshot', usetimestamp = true) {
  ensureDirectoryExists(screenshotDir);
  if (usetimestamp) {
    const timestamp = getHumanReadableTimestamp();
    prefix = `${prefix}_${timestamp}`;
  }
  const screenshotPath = path.join(screenshotDir, `${prefix}.png`);
  await page.screenshot({ path: screenshotPath, fullPage: true });
  info(`截图已保存: ${screenshotPath}`);
  return screenshotPath;
}

/**
 * 等待用户在控制台按下 Enter 键。
 * @returns {Promise<void>}
 */
export function waitForUserInput() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  return new Promise(resolve => {
    rl.question('', () => {
      rl.close();
      resolve();
    });
  });
}