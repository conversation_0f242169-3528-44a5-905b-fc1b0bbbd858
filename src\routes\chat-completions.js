import { readBody, createError } from 'h3';
import { validateChatCompletionRequest } from '../utils/validation.js';
import { setStreamHeaders } from '../middlewares/cors.js';
import { apiTokenAuth } from '../middlewares/auth.js';
import { processRequest, processRequestSync } from '../utils/app-processor.js';

/**
 * 处理 `/v1/chat/completions` 的 POST 请求。
 * 支持流式和非流式响应。
 * @param {import('h3').H3Event} event - H3 事件对象。
 * @returns {Promise<object|ReadableStream>} OpenAI 格式的响应对象或 SSE 流。
 */
export async function chatCompletionsHandler(event) {
  try {
    // 1. 认证
    apiTokenAuth(event);

    // 2. 读取和验证请求体
    const body = await readBody(event);
    const { prompt, systemPrompt, stream, model, temperature, messages } = validateChatCompletionRequest(body);

    // 3. 获取 APP_TOKEN token（如果在 APP_TOKEN 模式下）
    const appToken = event.context.appToken || null;

    // 4. 根据 stream 参数决定处理方式
    if (stream) {
      // 流式响应
      setStreamHeaders(event);

      const stream = new ReadableStream({
        start(controller) {
          // 将请求处理委托给 App 处理器，传递 APP_TOKEN token
          processRequest(controller, {
            model,
            temperature,
            systemPrompt,
            appToken,
            historyPrompt: prompt,
            messages: messages
          });
        }
      });

      return stream;

    } else {
      // 非流式响应
      const result = await processRequestSync({
        model,
        temperature,
        systemPrompt,
        appToken,
        historyPrompt: prompt,
        messages: messages
      });
      return result;
    }

  } catch (err) {
    console.error('处理聊天请求时出错:', err);
    // 重新抛出 H3 错误或将标准错误包装成 H3 错误
    if (err.statusCode) {
      throw err;
    }
    throw createError({
      statusCode: 500,
      statusMessage: err.message || 'An internal server error occurred.'
    });
  }
}