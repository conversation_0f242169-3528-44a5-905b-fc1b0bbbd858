import { createError } from 'h3';
import config from "../config.js";

/**
 * 提取消息内容，处理数组格式的 content
 * @param {string|Array} content - 消息内容，可能是字符串或数组
 * @returns {string} 提取的文本内容
 */
function extractMessageContent(content) {
  if (typeof content === 'string') {
    return content;
  }

  if (Array.isArray(content)) {
    return content.map(item => {
      if (item.type === 'text') {
        // 处理 OpenAI 格式的文本内容
        if (typeof item.text === 'string') {
          return item.text;
        } else if (item.text && typeof item.text.value === 'string') {
          return item.text.value;
        }
      }
      return '';
    }).filter(text => text.trim()).join(' ');
  }

  return '';
}

/**
 * 验证聊天完成请求体，并将其转换为内部处理格式。
 * @param {object} body - H3 请求体。
 * @returns {{prompt: string, systemPrompt: string, stream: boolean, model: string, temperature: number, messages: object[]}} 验证和格式化后的数据。
 * @throws {Error} 如果验证失败，则抛出 H3 错误。
 */
export function validateChatCompletionRequest(body) {
  const {
    messages,
    stream = false,
    model = config.app.defaultModel,
    temperature = config.app.temperature
  } = body;

  if (!messages || !Array.isArray(messages) || messages.length === 0) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid request: `messages` must be a non-empty array.'
    });
  }

  // 分离系统消息和其他消息
  let systemPrompt = '';
  const otherMessages = [];

  for (const message of messages) {
    if (!message.role || !message.content) {
      continue;
    }

    const content = extractMessageContent(message.content);
    if (!content.trim()) {
      continue;
    }
    otherMessages.push({
      role: message.role,
      content: content
    });
    // if (message.role === 'system') {
    //   // 如果有多个系统消息，合并它们
    //   if (systemPrompt) {
    //     systemPrompt += '\n\n' + content;
    //   } else {
    //     systemPrompt = content;
    //   }
    // } else {
    //   otherMessages.push({
    //     role: message.role,
    //     content: content
    //   });
    // }
  }


  const prompt = otherMessages.map(message => {
    // 添加角色标签以提供上下文
    return `${message.role}: ${message.content}`;
  }).join('\n\n');

  return {
    prompt,
    systemPrompt,
    stream,
    model,
    temperature,
    messages
  };
}