#!/bin/bash

# 清理可能存在的旧 X 服务器锁文件
echo "清理旧的 X 服务器锁文件..."
rm -f /tmp/.X99-lock 2>/dev/null || true
# 尝试清理 X11 socket，如果没有权限则忽略
rm -f /tmp/.X11-unix/X99 2>/dev/null || true

# 启动 Xvfb (虚拟显示服务器)
echo "启动 Xvfb 虚拟显示服务器..."
Xvfb :99 -screen 0 $XVFB_WHD -ac +extension GLX +render -noreset 2>/dev/null &
XVFB_PID=$!

# 等待 Xvfb 启动
sleep 3

# 验证 Xvfb 是否成功启动
if ! xdpyinfo -display :99 >/dev/null 2>&1; then
    echo "❌ Xvfb 启动失败，尝试重新启动..."
    sleep 2
    if ! xdpyinfo -display :99 >/dev/null 2>&1; then
        echo "❌ Xvfb 仍然无法启动，退出..."
        exit 1
    fi
fi
echo "✅ Xvfb 启动成功"

# 可选：启动窗口管理器 (fluxbox)
echo "启动窗口管理器..."
fluxbox &
FLUXBOX_PID=$!

# 可选：启动 VNC 服务器 (用于远程查看)
# 如果需要远程查看浏览器界面，可以取消注释下面的行
# echo "启动 VNC 服务器..."
# x11vnc -display :99 -nopw -listen localhost -xkb -ncache 10 -ncache_cr -forever &
# VNC_PID=$!

# 设置信号处理函数，确保优雅关闭
cleanup() {
    echo "正在关闭服务..."
    
    # 关闭 Node.js 应用
    if [ ! -z "$NODE_PID" ]; then
        kill $NODE_PID 2>/dev/null
        wait $NODE_PID 2>/dev/null
    fi
    
    # 关闭 VNC 服务器
    # if [ ! -z "$VNC_PID" ]; then
    #     kill $VNC_PID 2>/dev/null
    # fi
    
    # 关闭窗口管理器
    if [ ! -z "$FLUXBOX_PID" ]; then
        kill $FLUXBOX_PID 2>/dev/null
    fi
    
    # 关闭 Xvfb
    if [ ! -z "$XVFB_PID" ]; then
        kill $XVFB_PID 2>/dev/null
    fi
    
    echo "所有服务已关闭"
    exit 0
}

# 注册信号处理
trap cleanup SIGTERM SIGINT

# 启动 Node.js 应用
echo "启动 Node.js 应用..."
npm start &
NODE_PID=$!

# 等待 Node.js 应用结束
wait $NODE_PID

# 如果 Node.js 应用意外退出，执行清理
cleanup
