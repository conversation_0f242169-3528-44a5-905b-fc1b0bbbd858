# 容器内开发环境指南

本文档适用于在已有的 Docker 容器内（如远程 VSCode、GitHub Codespaces、云端开发环境等）开发和运行本项目的场景。

## 🎯 适用场景

- 远程 VSCode 开发环境
- GitHub Codespaces
- 云端开发容器
- 任何基于 Ubuntu/Debian 的开发容器

## 🚀 快速开始

### 1. 检查容器环境

首先确认您的容器环境：

```bash
# 检查操作系统
cat /etc/os-release

# 检查是否有 sudo 权限
sudo whoami

# 检查网络连接
curl -I https://www.google.com
```

### 2. 安装系统依赖

在容器内安装必要的系统依赖：

```bash
# 更新包管理器
sudo apt-get update

# 安装基础依赖
sudo apt-get install -y \
    wget \
    curl \
    unzip \
    xvfb \
    x11vnc \
    fluxbox \
    xterm \
    x11-utils \
    procps \
    git \
    python3 \
    python3-pip

# 安装 websockify (用于 noVNC)
sudo pip3 install websockify

# 下载并安装 noVNC
sudo mkdir -p /opt
cd /opt
sudo git clone https://github.com/novnc/noVNC.git
cd noVNC
sudo ln -s vnc.html index.html
```

### 3. 安装 Node.js 依赖

```bash
# 进入项目目录
cd /path/to/your/lmarena2api

# 安装项目依赖
npm install

# 安装 Playwright 浏览器
npx playwright install chromium --with-deps
npx patchright install chromium --with-deps
```

### 4. 创建启动脚本

创建一个适用于容器内环境的启动脚本：

```bash
# 创建启动脚本
cat > start-container-novnc.sh << 'EOF'
#!/bin/bash

# 设置环境变量
export DISPLAY=:99
export XVFB_WHD=1920x1080x24

# 清理可能存在的旧进程
pkill -f Xvfb || true
pkill -f x11vnc || true
pkill -f websockify || true
pkill -f fluxbox || true

# 等待清理完成
sleep 2

echo "🚀 启动容器内 noVNC 开发环境..."

# 启动 Xvfb (虚拟显示服务器)
echo "启动 Xvfb 虚拟显示服务器..."
Xvfb :99 -screen 0 $XVFB_WHD -ac +extension GLX +render -noreset &
XVFB_PID=$!

# 等待 Xvfb 启动
sleep 3

# 验证 Xvfb 是否成功启动
if ! xdpyinfo -display :99 >/dev/null 2>&1; then
    echo "❌ Xvfb 启动失败，尝试重新启动..."
    sleep 2
    if ! xdpyinfo -display :99 >/dev/null 2>&1; then
        echo "❌ Xvfb 仍然无法启动，退出..."
        exit 1
    fi
fi
echo "✅ Xvfb 启动成功"

# 启动窗口管理器 (fluxbox)
echo "启动窗口管理器..."
fluxbox &
FLUXBOX_PID=$!

# 启动 VNC 服务器
echo "启动 VNC 服务器..."
x11vnc -display :99 -nopw -listen 0.0.0.0 -xkb -ncache 10 -ncache_cr -forever -shared &
VNC_PID=$!

# 等待 VNC 服务器启动
sleep 2

# 启动 websockify (WebSocket 到 VNC 的代理)
echo "启动 websockify..."
websockify --web=/opt/noVNC 6080 localhost:5900 &
WEBSOCKIFY_PID=$!

# 等待 websockify 启动
sleep 2

echo "🌐 noVNC Web 界面已启动！"
echo "📱 通过浏览器访问: http://localhost:6080"
echo "🚀 应用服务将在: http://localhost:7860"
echo ""
echo "现在可以启动应用程序..."

# 启动应用程序
npm run dev
EOF

# 给脚本执行权限
chmod +x start-container-novnc.sh
```

### 5. 启动服务

```bash
# 启动 noVNC 和应用
./start-container-novnc.sh
```

## 🔗 访问方式

启动后，您可以通过以下方式访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| **noVNC Web 界面** | http://localhost:6080 | 图形界面访问 |
| **应用 API** | http://localhost:7860 | 主要应用服务 |

## 🛠️ 端口转发配置

### VSCode Remote Development

如果使用 VSCode 远程开发，需要配置端口转发：

1. 打开 VSCode 命令面板 (`Ctrl+Shift+P`)
2. 输入 "Forward a Port"
3. 添加端口：
   - `6080` (noVNC Web 界面)
   - `7860` (应用服务)

### GitHub Codespaces

Codespaces 会自动检测并转发端口，您会在终端看到端口转发的通知。

### 手动 SSH 端口转发

如果通过 SSH 连接到容器：

```bash
# 本地执行，转发端口到远程容器
ssh -L 6080:localhost:6080 -L 7860:localhost:7860 user@remote-host
```

## 🔧 故障排除

### 权限问题

如果遇到权限问题：

```bash
# 检查当前用户
whoami

# 检查用户组
groups

# 如果需要，将用户添加到相关组
sudo usermod -a -G video,audio $USER
```

### 显示问题

如果 Xvfb 启动失败：

```bash
# 检查 X11 相关进程
ps aux | grep -E "(Xvfb|x11vnc)"

# 手动清理
sudo pkill -f Xvfb
sudo rm -f /tmp/.X99-lock /tmp/.X11-unix/X99

# 重新启动
./start-container-novnc.sh
```

### 网络问题

检查端口占用：

```bash
# 检查端口是否被占用
netstat -tlnp | grep -E "(6080|7860|5900)"

# 或使用 ss 命令
ss -tlnp | grep -E "(6080|7860|5900)"
```

## 📝 环境变量配置

创建 `.env` 文件：

```bash
# 复制示例配置
cp .env.example .env

# 编辑配置
nano .env
```

常用环境变量：

```bash
# 浏览器配置
HEADLESS=false
BROWSER_TYPE=patchright

# 显示配置
DISPLAY=:99
XVFB_WHD=1920x1080x24

# 应用配置
PORT=7860
LOG_LEVEL=debug
```

## 🔄 后台运行

如果需要在后台运行服务：

```bash
# 使用 nohup 后台运行
nohup ./start-container-novnc.sh > novnc.log 2>&1 &

# 查看日志
tail -f novnc.log

# 停止服务
pkill -f "start-container-novnc.sh"
```

## 🎨 开发技巧

### 自动启动脚本

创建一个 `.vscode/tasks.json` 文件，在 VSCode 中快速启动：

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Start noVNC Development",
            "type": "shell",
            "command": "./start-container-novnc.sh",
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "new"
            },
            "problemMatcher": []
        }
    ]
}
```

### 快速重启脚本

创建重启脚本 `restart-services.sh`：

```bash
cat > restart-services.sh << 'EOF'
#!/bin/bash
echo "🔄 重启服务..."

# 停止所有相关进程
pkill -f Xvfb || true
pkill -f x11vnc || true
pkill -f websockify || true
pkill -f fluxbox || true
pkill -f "npm run dev" || true

# 等待进程完全停止
sleep 3

# 重新启动
./start-container-novnc.sh
EOF

chmod +x restart-services.sh
```

### 资源监控

监控容器资源使用：

```bash
# 创建监控脚本
cat > monitor-resources.sh << 'EOF'
#!/bin/bash
echo "📊 资源使用情况："
echo "CPU 使用率："
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

echo "内存使用情况："
free -h

echo "磁盘使用情况："
df -h /

echo "进程列表："
ps aux | grep -E "(Xvfb|x11vnc|websockify|node)" | grep -v grep
EOF

chmod +x monitor-resources.sh
```

## 🔐 安全注意事项

### 容器内安全

1. **端口绑定**：确保只绑定到 localhost
2. **防火墙**：如果容器有防火墙，确保正确配置
3. **用户权限**：避免使用 root 用户运行应用

### 网络安全

```bash
# 检查网络接口
ip addr show

# 检查监听端口
netstat -tlnp | grep -E "(6080|7860)"

# 确保只监听 localhost
# 应该看到 127.0.0.1:6080 而不是 0.0.0.0:6080
```

## 🚨 常见问题解决

### 问题 1: "Cannot connect to X server"

```bash
# 解决方案
export DISPLAY=:99
echo $DISPLAY

# 检查 Xvfb 是否运行
ps aux | grep Xvfb
```

### 问题 2: "Port already in use"

```bash
# 查找占用端口的进程
sudo lsof -i :6080
sudo lsof -i :7860

# 杀死占用进程
sudo kill -9 <PID>
```

### 问题 3: noVNC 连接失败

```bash
# 检查 websockify 状态
ps aux | grep websockify

# 检查 VNC 服务器
ps aux | grep x11vnc

# 重启 websockify
pkill -f websockify
websockify --web=/opt/noVNC 6080 localhost:5900 &
```

### 问题 4: 浏览器无法启动

```bash
# 检查 Playwright 安装
npx playwright --version
npx patchright --version

# 重新安装浏览器
npx playwright install chromium --with-deps
```

## 📋 完整示例：一键部署脚本

创建 `setup-container-dev.sh` 一键部署脚本：

```bash
cat > setup-container-dev.sh << 'EOF'
#!/bin/bash

set -e

echo "🚀 开始设置容器开发环境..."

# 1. 安装系统依赖
echo "📦 安装系统依赖..."
sudo apt-get update
sudo apt-get install -y wget curl unzip xvfb x11vnc fluxbox xterm x11-utils procps git python3 python3-pip

# 2. 安装 websockify
echo "🌐 安装 websockify..."
sudo pip3 install websockify

# 3. 安装 noVNC
echo "🖥️ 安装 noVNC..."
if [ ! -d "/opt/noVNC" ]; then
    sudo mkdir -p /opt
    cd /opt
    sudo git clone https://github.com/novnc/noVNC.git
    cd noVNC
    sudo ln -s vnc.html index.html
fi

# 4. 返回项目目录
cd - > /dev/null

# 5. 安装项目依赖
echo "📚 安装项目依赖..."
npm install

# 6. 安装浏览器
echo "🌐 安装浏览器..."
npx playwright install chromium --with-deps
npx patchright install chromium --with-deps

# 7. 创建启动脚本（如果不存在）
if [ ! -f "start-container-novnc.sh" ]; then
    echo "📝 创建启动脚本..."
    # 这里会包含之前创建的启动脚本内容
    # ... (启动脚本内容)
fi

echo "✅ 容器开发环境设置完成！"
echo ""
echo "🎯 下一步："
echo "1. 运行: ./start-container-novnc.sh"
echo "2. 访问: http://localhost:6080 (noVNC)"
echo "3. 访问: http://localhost:7860 (应用)"
echo ""
echo "💡 提示: 确保在 VSCode 中转发端口 6080 和 7860"
EOF

chmod +x setup-container-dev.sh
```

## 📚 相关文档

- [图形界面访问指南](./XVFB_USAGE.md)
- [项目主文档](./README.md)
- [VSCode Remote Development](https://code.visualstudio.com/docs/remote/remote-overview)
- [GitHub Codespaces](https://docs.github.com/en/codespaces)
